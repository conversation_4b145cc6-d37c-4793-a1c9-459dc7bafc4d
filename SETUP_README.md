## First time setup steps


#### Installing python 3.12
1. Download page [Python Downloads](https://www.python.org/downloads/)
2. From download page install python version 3.12
3. Make sure your system python version should be 3.12 (any minor version)
    ``python --version``
4. If python version is not python 3.12 then change your env variables accordingly


#### Installing [Poetry](https://python-poetry.org/)

1. Install pipx  ```pip install pipx```
2. Install poetry  ```pipx install poetry```
3. Set poetry config to always create virtual env inside project dir ```poetry config virtualenvs.in-project true```

#### Setting up Projects

1. Go to project root directory (dynamic-optimization-service/app)
2. Install project dependencies. This command will create a virtual env with all dependencies
```
poetry install
```


#### Intellij Setup

1. Install Python plugin (Restart required)
2. Go to File->Project Structure
3. Select Project from left panel
4. In sdk select Add Python SDK and select python.exe from virtual envs. _For eg {projectRootDir}/.venv/Scripts/python.exe_


## Adding python dependencies

Note: Following commands need to run in same directory where pyproject.toml file is present  
  
Instead of pip install. One should install dependencies using poetry  Following are some examples
1. latest version  
``poetry add pandas``
2. specific version  
``poetry add pandas@2.0.0``
3. In poetry, we can create group of dependencies.For example, we can have 2 groups main and dev. Dev group can
contain all dependencies used for development and testing. If you don't mention group by default it goes to main group  
e.g.
``poetry add pytest --group dev``


### References

1. [Poetry Commands](https://python-poetry.org/docs/cli/)
