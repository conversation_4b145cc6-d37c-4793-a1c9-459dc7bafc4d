services:

  do_app:
    build:
      context: .
      dockerfile: dockerfile
      args:
        DOCKER_BUILDKIT: 1
      target: runtime
    ports:
      - 8000:8000
    env_file:
      - "local.env"
    volumes:
      - .data:/app/files
    healthcheck:
      test: curl -f http://localhost:8000/health-check || exit 1
      retries: 5
      interval: 5s
    entrypoint: uvicorn src.controller.api:app --host 0.0.0.0 --port 8000

