import pytest
import boto3
from datetime import datetime
from app.src.common.evaluation_service import EvaluationService
from src.common.dto.evaluate_request import EvaluationRequestSource
from src.common.dto.evaluation_dynamodb_input import EvaluationDynamodbTableInput


@pytest.mark.integration
def test_evaluation_service_end_to_end(monkeypatch):
    """
    Integration test for EvaluationService:
      - Uses real AWS DynamoDB + S3 + SNS
      - Verifies evaluation executes without exceptions
      - Confirms result output is uploaded to S3
    """

    # GIVEN
    client_code = "Hilton"
    property_code = "REKCU"
    now = datetime.now().strftime("%Y_%m_%dT%H_%M_%S")

    # Prepare AWS SDK clients
    s3 = boto3.client("s3")
    sns = boto3.client("sns")
    dynamodb = boto3.client("dynamodb")

    bucket_name = "your-dop-evaluation-output-bucket"
    prefix = f"{client_code}/{property_code}/"

    # WHEN
    service = EvaluationService()

    request = EvaluationRequestSource(
        client_code=client_code,
        property_code=property_code,
        request_context=EvaluationDynamodbTableInput(
            delta_occ_solds="dyn-opt-property-incremental-solds",
            reference_rate="dyn-opt-ref-price-latest",
            delta_lrv="dyn-opt-delta-lrv-latest",
            min_data_requirement=21,
            max_data_limit=21,
            calibrated_potential="dyn-opt-calibrated-potentials"
        ),
    )

    # Run the actual service (real AWS access)
    service.evaluate(request)

    # THEN
    # Check that evaluation uploaded a log/result file to S3
    result = s3.list_objects_v2(Bucket=bucket_name, Prefix=prefix)
    assert "Contents" in result, f"No output found in S3 under prefix {prefix}"

    last_object = max(result["Contents"], key=lambda obj: obj["LastModified"])
    key = last_object["Key"]
    print(f"✅ Found uploaded evaluation file: s3://{bucket_name}/{key}")

    # Optional: check SNS notifications (if expected)
    # Example: confirm topic received a message (requires test topic or subscription)
    # topic_arn = "arn:aws:sns:ap-south-1:123456789012:your-topic"
    # subscriptions = sns.list_subscriptions_by_topic(TopicArn=topic_arn)
    # assert subscriptions["Subscriptions"], "SNS topic not active or no subscriptions"

    # Optional: fetch S3 file content and validate format
    body = s3.get_object(Bucket=bucket_name, Key=key)["Body"].read().decode()
    assert "assemblage" in key or "csv" in key
    assert len(body) > 0, "S3 file uploaded but empty"

    print(f"✅ Integration test passed for {client_code}/{property_code}")
