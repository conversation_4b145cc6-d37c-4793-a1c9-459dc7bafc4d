version: 2
registries:
  ideas-artifactory:
    type: "maven-repository"
    url: "https://ideasavm.jfrog.io/artifactory/libs-release-virtual"
    username: ${{ secrets.ARTIFACTORY_USER }}
    password: ${{ secrets.ARTIFACTORY_API_KEY }}

updates:
  # Maintain dependencies for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "daily"

  # Maintain dependencies for maven
  - package-ecosystem: "maven"
    directory: "/"
    registries: "*"
    open-pull-requests-limit: 3
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]
    schedule:
      interval: "daily"
