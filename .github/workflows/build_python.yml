name: "Build"

permissions:
  contents: read
  issues: read
  checks: write
  pull-requests: write
  packages: write
  id-token: write

defaults:
  run:
    shell: bash

on:
  workflow_call:
    inputs:
      ##############################
      #       General
      ##############################
      github_auth_app:
        description: GitHub App to use for authentication.
        required: false
        default: "default"
        type: string
      service_name:
        description: "Service name"
        required: true
        type: string
      service_version:
        description: "Service version"
        required: false
        default: ${{ github.event.pull_request.head.sha || github.sha }}
        type: string
      checkout_ref:
        description: "Git ref to checkout"
        required: false
        default: ${{ github.event.pull_request.head.sha || github.sha }}
        type: string


      ###################
      # Python
      ###################
      python_version:
        description: "Python version to install"
        required: false
        default: "3.12"
        type: string
      poetry_version:
        description: "Poetry version to install"
        required: false
        default: "1.7"
        type: string
      poetry_tests:
        description: "Whether to run unit tests (pytest)"
        required: false
        default: "true"
        type: string
      poetry_lint:
        description: "Whether to run linting (poetry run lint)"
        required: false
        default: "true"
        type: string
      lint_args:
        description: "Extra args passed to poetry run lint command."
        required: false
        default: ""
        type: string
      poetry_custom_commands:
        description: >
          A multi-line string representing any custom poetry commands to run instead of predefined ones.
        required: false
        type: string
      only_production_no_tests:
        description: "Install only production dependencies and skip tests"
        required: false
        default: "false"
        type: string

#      ##############################
#      #       Sonar
#      ##############################
#      sonar_project_key:
#        description: "Sonar project key"
#        required: false
#        default: ${{ github.repository_owner }}_${{ github.event.repository.name }}
#        type: string
#      sonar_no_fail:
#        description: "Do not fail if Sonar Quality Gate fails"
#        required: false
#        default: false
#        type: boolean
#      sonar_metadata_report_file:
#        description: "Path to Sonar metadata report file (report-task.txt)"
#        required: false
#        default: "default"
#        type: string
#      # TODO: once we are sure that no one uses this input, we can remove it and use runner_labels only
      runner_os:
        description: "[deprecated use runner_labels instead]"
        required: false
        default: "ubuntu-latest"
        type: string
      runner_labels:
        description: "List of runners labels (should be in a form of stringified JSON)"
        required: false
        default: "['ubuntu-latest']"
        type: string
      next_workflow:
        description: "Next workflow name"
        required: false
        type: string
      next_workflow_ref:
        description: "Git ref for next workflow trigger"
        required: false
        default: ${{ github.head_ref }}
        type: string
      next_workflow_inputs:
        description: "Inputs for next workflow"
        required: false
        default: "{}"
        type: string
      create_status_check:
        description: "Whether to create status check for this workflow"
        required: false
        default: "false"
        type: string
      workflow_jobs_timeout:
        description: "The maximum number of minutes to let a job run before GitHub automatically cancels it"
        required: false
        default: 120
        type: number

      ##############################
      #      Unit tests
      ##############################
      publish_tests_results:
        description: "Whether to publish test results"
        required: false
        default: "true"
        type: string
      publish_tests_results_name:
        description: "Name of the test results"
        required: false
        default: "Unit Test Results"
        type: string
      tests_files:
        description: "File patterns of test result files. Supports *, **, ?, and []. Use multiline string for multiple patterns"
        required: false
        default: "./**/surefire-reports/**/*.xml"
        type: string

      ##############################
      #      Docker
      ##############################
      docker_platform:
        description: "Docker image platform"
        required: false
        default: "linux/amd64"
        type: string
      docker_image:
        description: "Whether to build a Docker image and push it to GHCR"
        required: false
        default: true
        type: boolean
      dockerfile:
        description: "Dockerfile name"
        required: false
        default: "Dockerfile.avm"
        type: string
      docker_image_scan:
        description: "Whether to scan docker image for vulnerabilities"
        required: false
        default: false
        type: boolean

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    name: "Build python app"
    # TODO: once we are sure that no one uses the old runner_os, we can remove it and use runner_labels only
    runs-on: ${{ !contains(inputs.runner_labels, 'ubuntu-latest') && fromJSON(inputs.runner_labels) || inputs.runner_os }}
    steps:
      - name: "Inputs summary"
        if: ${{ runner.debug == '1' }}
        run: |
          echo "**Inputs**" >> $GITHUB_STEP_SUMMARY
          echo \`\`\`yaml >> $GITHUB_STEP_SUMMARY
          echo "${{ toJSON(inputs) }}" >> $GITHUB_STEP_SUMMARY
          echo \`\`\` >> $GITHUB_STEP_SUMMARY

      - name: "[${{ inputs.github_auth_app }}] GitHub auth token"
        uses: ideasorg/ideas-github-auth-action@v1.5.0
        id: generate_token
        with:
          default_github_app: ${{ vars.IDEAS_DEFAULT_GITHUB_APP }}
          github_auth_app: ${{ inputs.github_auth_app }}

      - name: "Checkout [${{ github.repository }}]"
        uses: actions/checkout@v4.1.7
        with:
          # Disabling shallow clone is recommended for improving relevancy of reporting
          ref: ${{ inputs.checkout_ref }}
          fetch-depth: 0
          show-progress: false

      - name: "Install Poetry ${{ matrix.lambda_name }}"
        run: |
          pipx install poetry==${{ inputs.poetry_version }}

      - name: "Setup Python [${{ inputs.python_version }} - ${{ matrix.lambda_name }}]"
        uses: actions/setup-python@v5.1.1
        with:
          python-version: ${{ inputs.python_version }}
          cache: "poetry"
          cache-dependency-path: app/poetry.lock

      - name: "Build and Test - ${{ matrix.lambda_name }}"
        id: build_and_test
        if: ${{ inputs.poetry_custom_commands == '' }}
        env:
          PYTHON_EXECUTABLE_PATH: Python/${{ inputs.python_version }}/x64/bin/python
        run: |
          echo "::group::Poetry configuration"
          echo "Configuring poetry"
          poetry config virtualenvs.in-project true
          echo "::endgroup::"

          if [[ "${{ inputs.only_production_no_tests }}" == 'false' ]]; then
            echo "::group::Poetry install"
            echo "Installing project dependencies"
            poetry install
            echo "::endgroup::"

            if [[ "${{ inputs.poetry_lint }}" == 'true' ]]; then
              echo "::group::Poetry lint"
              echo "Running lints"
              poetry run lint ${{ inputs.lint_args }}
              echo "::endgroup::"
            fi

            if [[ "${{ inputs.poetry_tests }}" == 'true' ]]; then
              echo "::group::Poetry test"
              echo "Running tests"
              poetry run coverage run -m pytest
              echo "::endgroup::"

              echo "Running coverage report"
              echo "Coverage report"
              poetry run coverage xml
              echo "::endgroup::"
            fi
          fi
        working-directory: app

      - name: "Run custom poetry command"
        id: poetry_custom_commands
        if: ${{ inputs.poetry_custom_commands != '' }}
        run: |
          ${{ inputs.poetry_custom_commands }}
        working-directory: app

      #INstall python, poetry, run tests

      # Something related to sonar

      - name: "[Debug] Show folders and files"
        if: always()
        run: |
          echo "::group::Folders and files"
          tree -a
          echo "::endgroup::"
        working-directory: ${{ github.workspace }}

#      - name: "Publish Unit Test Results"
#        uses: EnricoMi/publish-unit-test-result-action@v2.17.0
#        if: |
#          always() &&
#          inputs.publish_tests_results == 'true'
#        with:
#          commit: ${{ inputs.checkout_ref }}
#          check_name: ${{ inputs.publish_tests_results_name }}
#          junit_files: ${{ inputs.tests_files }}

      - name: "Setup Docker buildx"
        if: inputs.docker_image
        uses: docker/setup-buildx-action@v3.6.1
        with:
          version: latest

      - name: "Login to DockerHub registry"
        if: inputs.docker_image
        uses: docker/login-action@v3.3.0
        with:
          username: "ideastechops"
          password: ${{ secrets.IDEAS_DOCKER_PASSWORD }}

      - name: "Login to GitHub Container Registry"
        if: inputs.docker_image
        uses: docker/login-action@v3.3.0
        with:
          registry: "ghcr.io"
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: "Extract Docker metadata"
        if: inputs.docker_image
        id: meta
        uses: docker/metadata-action@v5.5.1
        with:
          images: ghcr.io/${{ github.repository }}
          tags: |
            type=raw,value=${{ inputs.service_version }},enable=true
            type=raw,value=main-${{ github.run_id }},enable={{is_default_branch}}
            type=raw,value=latest,enable={{is_default_branch}}

      - name: "Build Docker image"
        id: docker_build
        if: inputs.docker_image
        uses: docker/build-push-action@v5.3.0
        with:
          context: .
          file: ${{ inputs.dockerfile }}
          push: true
          provenance: false
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          platforms: ${{ inputs.docker_platform }}
          cache-from: type=gha
          cache-to: type=gha

      ############################################################################################################
      # Docker image scan
      ############################################################################################################
      - name: "Pull image"
        if: inputs.docker_image_scan && inputs.docker_image
        run: docker pull ghcr.io/${{ github.repository }}:${{ inputs.service_version }}

      - name: "Prisma Cloud image scan"
        if: inputs.docker_image_scan && inputs.docker_image
        continue-on-error: true
        id: scan
        uses: PaloAltoNetworks/prisma-cloud-scan@v1.6.7
        with:
          pcc_console_url: ${{ secrets.PRISMA_CLOUD_URL }}
          pcc_user: ${{ secrets.PRISMA_CLOUD_USERNAME }}
          pcc_pass: ${{ secrets.PRISMA_CLOUD_PASSWORD }}
          image_name: ghcr.io/${{ github.repository }}:${{ inputs.service_version }}

      # IMPORTANT: can be used only when GHAS is enabled
      # - name: 'Upload SARIF file'
      #   if: |
      #     always() &&
      #     inputs.docker_image_scan
      #   uses: github/codeql-action/upload-sarif@v2.1.26
      #   with:
      #     sarif_file: ${{ steps.scan.outputs.sarif_file }}

      ########################################################################################################################
      # Results in a job summary
      ########################################################################################################################
#      - name: "Job Summary & final result"
#        if: always()
#        run: |
#          if [[ "${{ inputs.build_tool }}" == 'maven' ]]; then
#            if [[ "${{ steps.maven_build.conclusion }}" == 'success' ]]; then
#              echo "✅ Maven build & test" >> $GITHUB_STEP_SUMMARY
#            else
#              echo "❌ Maven build & test" >> $GITHUB_STEP_SUMMARY
#            fi
#          elif [[ "${{ inputs.build_tool }}" == 'gradle' ]]; then
#            if [[ "${{ steps.gradle_build.conclusion }}" == 'success' ]]; then
#              echo "✅ Gradle build & test" >> $GITHUB_STEP_SUMMARY
#            else
#              echo "❌ Gradle build & test" >> $GITHUB_STEP_SUMMARY
#            fi
#          fi
#
#          if [[ "${{ steps.docker_build.conclusion }}" == 'success' ]]; then
#            echo "✅ Docker build" >> $GITHUB_STEP_SUMMARY
#          elif [[ "${{ steps.docker_build.conclusion }}" == 'skipped' ]]; then
#            echo "⏭️ Docker build (skipped)" >> $GITHUB_STEP_SUMMARY
#          else
#            echo "❌ Docker build" >> $GITHUB_STEP_SUMMARY
#          fi
#
#          if [[ "${{ inputs.sonar_no_fail }}" != 'true' && "${{ steps.sonar_quality_gate.conclusion }}" != 'skipped' ]]; then
#            if [[ "${{ steps.sonar_quality_gate.outputs.quality-gate-status }}" == 'PASSED' ]]; then
#              echo "✅ Sonar Quality Gate" >> $GITHUB_STEP_SUMMARY
#            elif [[ "${{ steps.sonar_quality_gate.outputs.quality-gate-status }}" == 'WARN' ]]; then
#              echo "⚠️ Sonar Quality Gate" >> $GITHUB_STEP_SUMMARY
#            elif [[ "${{ steps.sonar_quality_gate.outputs.quality-gate-status }}" == 'FAILED' ]]; then
#              echo "❌ Sonar Quality Gate" >> $GITHUB_STEP_SUMMARY
#            fi
#          else
#            echo "⏭️ Sonar Quality Gate" >> $GITHUB_STEP_SUMMARY
#          fi
#
#          if [[ "${{ steps.scan.outcome }}" == 'success' ]]; then
#            echo "✅ Prisma Cloud image scan" >> $GITHUB_STEP_SUMMARY
#          elif [[ "${{ steps.scan.outcome }}" == 'skipped' ]]; then
#            echo "⏭️ Prisma Cloud image scan (skipped)" >> $GITHUB_STEP_SUMMARY
#          else
#            echo "❌ Prisma Cloud image scan" >> $GITHUB_STEP_SUMMARY
#          fi
#
#        shell: bash
