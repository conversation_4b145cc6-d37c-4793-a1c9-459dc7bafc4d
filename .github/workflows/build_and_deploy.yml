name: "Build & Deploy"

on:
  pull_request:
  push:
    branches:
      - 'main'

jobs:
  ## -------------------------------------------------------------------------------------------------------------------
  # Unfortunately we can't use ENV in reusable workflows like:
  # uses: ./.github/workflows/reusable_build.yml
  #    with:
  #      aws_account_id: ${{ env.AWS_ACCOUNT_ID_DEV }}
  # This is a GitHub limitation we should deal with right now
  # Here are some links that might be updated in the future with more elegant solutions:
  # https://github.community/t/passing-environment-variables-to-reusable-workflow/230456/5
  # https://github.com/actions/runner/issues/480
  ## -------------------------------------------------------------------------------------------------------------------
  params:
    name: 'Set Environment Variables'
    runs-on: 'ubuntu-latest'
    outputs:
      params: ${{ steps.env-vars.outputs.env_vars }}
    steps:
      - id: env-vars
        uses: ideasorg/ideas-github-set-env-action@v0

  verify:
    if: github.event_name == 'pull_request'
    needs:
      - params
    uses: ideasorg/ideas-github-workflows/.github/workflows/lints_and_checks.yml@v0
    with:
      tf_precommit: ${{ fromJSON(needs.params.outputs.params).TF_FMT_AND_DOCS }}
      tf_validate: ${{ fromJSON(needs.params.outputs.params).TF_VALIDATE }}
      tf_lint: ${{ fromJSON(needs.params.outputs.params).TFLint }}
      tf_sec: ${{ fromJSON(needs.params.outputs.params).TFSec }}
      tf_dir: ${{ fromJSON(needs.params.outputs.params).TF_DIR }}
    secrets: inherit

  ## -------------------------------------------------------------------------------------------------------------------
  #  Build job is a general job. It doesn't relate to any environment.
  #  It builds the project, run unit tests
  ## -------------------------------------------------------------------------------------------------------------------
  PYTHON_BUILD:
    needs:
      - params
    uses: ./.github/workflows/build_python.yml
    with:
      poetry_tests: "false"
      service_name: ${{ fromJSON(needs.params.outputs.params).SERVICE_NAME }}
      poetry_lint: ${{ !fromJSON(needs.params.outputs.params).MAIN_BRANCH }} # don't run lint on `main`
      lint_args: 'src tests'
      python_version: '3.12.0'
      dockerfile: dockerfile
      only_production_no_tests: ${{ fromJSON(needs.params.outputs.params).MAIN_BRANCH }}
    secrets: inherit

  ## -------------------------------------------------------------------------------------------------------------------
  #  Get latest commit sha (service version) from merged PR
  #  and set a new image version with merged commit sha (commit on main branch)
  # It gives the ability to use one image across all environments without rebuilding
  ## -------------------------------------------------------------------------------------------------------------------
  RELEASE_MAIN_IMAGE:
    if: |
      github.ref == 'refs/heads/main' && !contains(github.actor, '[bot]')
    uses: ideasorg/ideas-github-workflows/.github/workflows/springboot_release_main.yml@v0
    secrets: inherit

  ## -------------------------------------------------------------------------------------------------------------------
  #  DEV environment Docker image push, PLAN, APPLY
  ## -------------------------------------------------------------------------------------------------------------------
  DEV_DEPLOY:
    if: github.event_name == 'pull_request'
    needs:
      - params
      - PYTHON_BUILD
      - verify
    uses: ideasorg/ideas-github-workflows/.github/workflows/springboot_deploy.yml@v0
    with:
      environment: 'dev'
      aws_account_id: ${{ fromJSON(needs.params.outputs.params).AWS_ACCOUNT_ID_DEV }}
      service_name: ${{ fromJSON(needs.params.outputs.params).SERVICE_NAME }}
      team_name: ${{ fromJSON(needs.params.outputs.params).TEAM_NAME }}
      teams_channel_email: ${{ fromJSON(needs.params.outputs.params).TEAMS_CHANNEL_EMAIL }}
      tf_dir: ${{ fromJSON(needs.params.outputs.params).TF_DIR }}
      tf_state_name: ${{ fromJSON(needs.params.outputs.params).TF_STATE_NAME }}
      tf_ecr_resource_name: "module.dyn_opt_analytics_service.aws_ecr_repository.this"
      tf_plan_args: '-var-file=env/dev.tfvars'
      initial_run: ${{ fromJSON(needs.params.outputs.params).INITIAL_RUN }}
    secrets: inherit

  ## -------------------------------------------------------------------------------------------------------------------
  #  STAGE environment Docker image push, PLAN, APPLY
  ## -------------------------------------------------------------------------------------------------------------------
  STAGE_DEPLOY:
    if: |
      github.ref == 'refs/heads/main' && !contains(github.actor, '[bot]')
    needs:
      - params
#      - RELEASE_MAIN_IMAGE
    uses: ideasorg/ideas-github-workflows/.github/workflows/springboot_deploy.yml@v0
    with:
      environment: 'stage'
      aws_account_id: ${{ fromJSON(needs.params.outputs.params).AWS_ACCOUNT_ID_STAGE }}
      service_name: ${{ fromJSON(needs.params.outputs.params).SERVICE_NAME }}
      team_name: ${{ fromJSON(needs.params.outputs.params).TEAM_NAME }}
      teams_channel_email: ${{ fromJSON(needs.params.outputs.params).TEAMS_CHANNEL_EMAIL }}
      tf_dir: ${{ fromJSON(needs.params.outputs.params).TF_DIR }}
      tf_state_name: ${{ fromJSON(needs.params.outputs.params).TF_STATE_NAME }}
      tf_ecr_resource_name: "module.dyn_opt_analytics_service.aws_ecr_repository.this"
      tf_plan_args: '-var-file=env/stage.tfvars'
      initial_run: ${{ fromJSON(needs.params.outputs.params).INITIAL_RUN }}
    secrets: inherit

  ## -------------------------------------------------------------------------------------------------------------------
  #  PROD environment Docker image push, PLAN, APPLY
  ## -------------------------------------------------------------------------------------------------------------------
  PROD_DEPLOY:
    if: |
      github.ref == 'refs/heads/main' && !contains(github.actor, '[bot]')
    needs:
      - params
      - STAGE_DEPLOY
    uses: ideasorg/ideas-github-workflows/.github/workflows/springboot_deploy.yml@v0
    with:
      environment: 'prod'
      aws_account_id: ${{ fromJSON(needs.params.outputs.params).AWS_ACCOUNT_ID_PROD }}
      service_name: ${{ fromJSON(needs.params.outputs.params).SERVICE_NAME }}
      team_name: ${{ fromJSON(needs.params.outputs.params).TEAM_NAME }}
      teams_channel_email: ${{ fromJSON(needs.params.outputs.params).TEAMS_CHANNEL_EMAIL }}
      tf_dir: ${{ fromJSON(needs.params.outputs.params).TF_DIR }}
      tf_state_name: ${{ fromJSON(needs.params.outputs.params).TF_STATE_NAME }}
      tf_ecr_resource_name: "module.dyn_opt_analytics_service.aws_ecr_repository.this"
      tf_plan_args: '-var-file=env/prod.tfvars'
      initial_run: ${{ fromJSON(needs.params.outputs.params).INITIAL_RUN }}
    secrets: inherit
