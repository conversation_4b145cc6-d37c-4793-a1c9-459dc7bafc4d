name: "[Manual] Deploy"

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: 'Run Deploy on ENV'
        options:
          - dev
          - stage
          - prod
        required: true
      commit_sha:
        type: string
        description: 'Commit SHA to deploy'
        required: true
      force:
        description: 'Force Deployment (Check if you want to perform a force ECS deployment)'
        required: false
        default: false
        type: boolean
      resurrect_from_ecr:
        description: 'Resurrect image from ECR before deploy'
        default: false
        required: false
        type: boolean

jobs:
  ## -------------------------------------------------------------------------------------------------------------------
  # Unfortunately we can't use ENV in reusable workflows like:
  # uses: ./.github/workflows/reusable_build.yml
  #    with:
  #      aws_account_id: ${{ env.AWS_ACCOUNT_ID_DEV }}
  # This is a GitHub limitation we should deal with right now
  # Here are some links that might be updated in the future with more elegant solutions:
  # https://github.community/t/passing-environment-variables-to-reusable-workflow/230456/5
  # https://github.com/actions/runner/issues/480
  ## -------------------------------------------------------------------------------------------------------------------
  params:
      name: 'Set Environment Variables'
      runs-on: 'ubuntu-latest'
      outputs:
        params: ${{ steps.env-vars.outputs.env_vars }}
      steps:
        - id: env-vars
          uses: ideasorg/ideas-github-set-env-action@v0
          with:
            environment: ${{ inputs.environment }}

  ECR_TO_GHCR:
    if: inputs.resurrect_from_ecr == 'true'
    needs:
      - params
    uses: ideasorg/ideas-github-workflows/.github/workflows/ecr_to_ghcr.yml@v0
    with:
      aws_account_id: ${{ fromJSON(needs.params.outputs.params).AWS_ACCOUNT_ID }}
      image_name: ${{ fromJSON(needs.params.outputs.params).SERVICE_NAME }}
      image_tag: ${{ inputs.commit_sha || github.sha }}
    secrets: inherit

  DEPLOY:
    if: |
      !failure() &&
      !cancelled()
    needs:
      - params
      - ECR_TO_GHCR
    uses: ideasorg/ideas-github-workflows/.github/workflows/springboot_deploy.yml@v0
    with:
      environment: ${{ inputs.environment }}
      aws_account_id: ${{ fromJSON(needs.params.outputs.params).AWS_ACCOUNT_ID }}
      service_name: ${{ fromJSON(needs.params.outputs.params).SERVICE_NAME }}
      service_version: ${{ inputs.commit_sha }}
      tf_dir: ${{ fromJSON(needs.params.outputs.params).TF_DIR }}
      tf_state_name: ${{ fromJSON(needs.params.outputs.params).TF_STATE_NAME }}
      tf_plan_args: '-var-file=env/${{ inputs.environment }}.tfvars'
      initial_run: ${{ fromJSON(needs.params.outputs.params).INITIAL_RUN }}
      force: ${{ inputs.force == 'true' }}
    secrets: inherit
