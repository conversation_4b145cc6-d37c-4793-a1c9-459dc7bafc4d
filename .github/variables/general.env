# AWS Accounts IDs per ENV
AWS_ACCOUNT_ID_DEV=************
AWS_ACCOUNT_ID_STAGE=************
AWS_ACCOUNT_ID_PROD=************

# Terraform related
TF_DIR=infra
TF_STATE_NAME=dyn-opt-analytics-service
# Service related
SERVICE_NAME=dyn-opt-analytics

# DataDog related
TEAM_NAME=ans
TEAMS_CHANNEL_EMAIL=<EMAIL> ## Check


# This is a specific variable that should be set to true only the first run (build/deploy) for each environment
# It handles a chicken and egg situation with AWS ECR and Pushing docker image in it
INITIAL_RUN=true


# Lints and checks
TF_FMT_AND_DOCS=true
TF_VALIDATE=true
TFLint=true
TFSec=true
