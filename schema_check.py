import boto3

def get_table_key_schema(table_name: str):
    dynamodb = boto3.client('dynamodb')

    response = dynamodb.describe_table(TableName=table_name)
    key_schema = response['Table']['KeySchema']

    print(f"Key schema for table '{table_name}':")
    for key in key_schema:
        print(f" - {key['KeyType']}: {key['AttributeName']}")

    return key_schema

# Example usage
get_table_key_schema('dyn-opt-property-last-incremental-time')
