import json

import requests
from requests import Response

from src.common.exceptions.http_exception import HttpException


class HttpService:

    def __init__(self, base_url: str, authorizer=None):
        self.base_url = base_url
        self.authorizer = authorizer

    def __get_full_url(self, url: str):
        return f'{self.base_url}/{url}'

    def get_raw_response(self, path: str, params=None, **kwargs) -> Response:
        """kwargs which `requests.get` accepts"""

        full_url = self.__get_full_url(path)
        res = requests.get(full_url, auth=self.authorizer, params=params, **kwargs)
        if res.status_code == 401:
            self.refresh_token()
            res = requests.get(full_url, auth=self.authorizer, params=params, **kwargs)
        return res

    def refresh_token(self):
        self.authorizer.refresh_token()

    def post(self, path: str, body: dict, **kwargs):
        """kwargs which `requests.post` accepts"""
        full_url = self.__get_full_url(path)
        res = requests.post(full_url, data=json.dumps(body), auth=self.authorizer, **kwargs)
        if res.status_code == 401:
            self.refresh_token()
            res = requests.post(full_url, data=json.dumps(body), auth=self.authorizer, **kwargs)
        if not res.ok:
            raise HttpException(f"Request to {full_url} failed with status code {res.status_code} for1 {res.reason}")
        return res.json()
