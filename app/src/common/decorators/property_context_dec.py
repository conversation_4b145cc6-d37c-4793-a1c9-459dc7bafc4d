import functools
from typing import Callable, Any

from src.common.base_classes.property_ctx_settable import PropertyContextSettable
from src.common.dto.property_context_info import PropertyContextInfo
from src.context_vars.property_context import property_context_var


def set_context_from_args(func: Callable[[...], Any]) -> Callable[..., Any]:
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        settable = [a for a in args if isinstance(a, PropertyContextSettable)]
        if len(settable) <= 0:
            settable = [v for k, v in kwargs if isinstance(v, PropertyContextSettable)]
        if len(settable) == 0:
            result = func(*args, **kwargs)
            return result

        property_context_var.set(PropertyContextInfo(property_code=settable[0].get_property_code(), client_code=settable[0].get_client_code()))
        result = func(*args, **kwargs)
        property_context_var.set(PropertyContextInfo(property_code='DEFAULT_CODE', client_code='DEFAULT_CODE'))
        return result
    return wrapper
