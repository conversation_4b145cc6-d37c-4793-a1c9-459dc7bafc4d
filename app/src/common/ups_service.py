import logging

from pydantic_core.core_schema import none_schema
from src.common.dto.property_info import PropertyInfo
from src.common.enums.env_var import EnvironmentVariable
from src.common.env import get_value
from src.common.http_authorizers.ucs_auth import UCSAuth
from src.common.http_authorizers.uis_auth import UISAuth
from src.common.http_service import HttpService


class UPSService:
    GET_PROPERTY_SEARCH_PATH = 'v2/properties/search'

    def __init__(self, ups_http_service: HttpService):
        self.ups_http_service = ups_http_service
        self.logger = logging.getLogger(__name__)

    def extract_property_info(self,client_code, property_code, data) -> PropertyInfo | None:
        return PropertyInfo(
            client_code = client_code,
            property_code = property_code,
            time_zone=data.get("timezone", ""),
            tier = data['customAttributesJson'].get('Tier', ''),
            brand_code = data['customAttributesJson'].get('BRAND CODE', ''),
            global_area = data['customAttributesJson'].get('Global Area', ''),
            location_type = data['customAttributesJson'].get('Location Type', ''),
            g3_host_url = data['appConfigJson']["g3"].get("hostUrl", '').replace("/solutions", "")
        )

    def get_param(self, client_code: str, property_code: str)  -> PropertyInfo | None:
        request = {
            'clientCode': client_code.upper(),
            'propertyCode': property_code.upper()
        }
        response = self.ups_http_service.post(self.GET_PROPERTY_SEARCH_PATH, body=request)
        data = response[0]
        return self.extract_property_info(client_code, property_code, data)

ups_service = UPSService(HttpService(get_value(EnvironmentVariable.UPS_BASE_URL, ''),
                                     authorizer=UCSAuth(HttpService(get_value(EnvironmentVariable.UIS_AUTH_URL, ''),
                                                                    authorizer=UISAuth()))))

def get_property_timezone(client_code: str, property_code: str):
    return get_property_info(client_code, property_code).time_zone

def get_property_info(client_code: str, property_code: str):
    return ups_service.get_param(client_code=client_code, property_code=property_code)

def get_property_host_url(client_code: str, property_code: str):
    return get_property_info(client_code, property_code).g3_host_url


if __name__ == '__main__':
    get_property_timezone('Hilton', 'TPANH')
    get_property_host_url('Hilton', 'TPANH')
    get_property_info('Hilton', 'TPANH')