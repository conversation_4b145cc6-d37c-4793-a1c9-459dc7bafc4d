import logging
from datetime import datetime, timedelta

from pydantic import BaseModel, ConfigDict
from requests import Request
from requests.auth import AuthBase

from src.common.enums.env_var import EnvironmentVariable
from src.common.env import get_value_or_default
from src.common.http_service import HttpService
from src.common.pydantic_util import from_dict


class UISTokenResponse(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    access_token: str
    expires_in: int
    token_type: str


class UCSAuth(AuthBase):

    def __call__(self, request: Request):
        token = get_value_or_default(EnvironmentVariable.FDS_UCS_TOKEN, self.get_token)
        request.headers['Authorization'] = f'Bearer {token}'
        return request

    def __init__(self, uis_http_service: HttpService):
        self.uis_http_service = uis_http_service
        self.__token = None
        self.__token_valid_until = None
        self.logger = logging.getLogger(__name__)

    def get_token(self):
        if self.__token is None or self.__token_valid_until is None or not self.is_current_token_valid():
            self.refresh_token()
        return self.__token

    def is_current_token_valid(self):
        return datetime.now() < self.__token_valid_until

    def refresh_token(self):
        self.logger.info("Refreshing token")
        res = self.get_fds_auth_token()
        self.__token_valid_until = datetime.now() + timedelta(seconds=res.expires_in)
        self.__token = res.access_token

    def get_fds_auth_token(self) -> UISTokenResponse:
        auth_path = 'internal_m2m/oauth2/token'
        params = {
            'grant_type': 'client_credentials',
            'scope': 'com.ideas.casper/ideasApiClient'
        }
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        return from_dict(UISTokenResponse,
                         self.uis_http_service.post(f'{auth_path}', params=params, headers=headers, body={}))
