from typing import TypeVar

from pydantic import TypeAdapter, BaseModel

base_model_type = TypeVar('base_model_type', bound=BaseModel)


def from_json(model: type[base_model_type], data: str) -> base_model_type:
    return TypeAdapter(model).validate_json(data)


def from_dict_array(model: type[list[base_model_type]], data: list[dict]) -> list[base_model_type]:
    return TypeAdapter(model).validate_python(data)


def from_dict(model: type[base_model_type], data: dict) -> base_model_type:
    return TypeAdapter(model).validate_python(data)


def to_json(model: type[base_model_type], data: base_model_type) -> bytes:
    return TypeAdapter(model).dump_json(data)


def to_json_string(model: base_model_type, by_alias=True) -> str:
    return model.model_dump_json(by_alias=by_alias)


def to_dict(model: type[base_model_type], data: base_model_type) -> bytes:
    return TypeAdapter(model).dump_python(data)


def to_dict_array(model: type[list[base_model_type]], data: list[base_model_type]) -> list[dict]:
    return TypeAdapter(model).dump_python(data, by_alias=True)


def to_json_array(model: type[list[base_model_type]], data: list[base_model_type]) -> bytes:
    return TypeAdapter(model).dump_json(data)
