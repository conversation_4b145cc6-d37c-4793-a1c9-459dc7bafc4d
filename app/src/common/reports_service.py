import logging
from datetime import date, timedelta, datetime
from io import Bytes<PERSON>
from pathlib import Path
import asyncio

import numpy as np
import pandas as pd

from src.algorithm.Utilities.GenericUtilities.fastDateRange import fastDateRange
from src.common.dto.DecisionAnalysisReports import DecisionAnalysisReports
from src.common.dto.property_info import PropertyInfo
from src.common.dynamodb_service import dynamodb_service
from src.common.enums.env_var import EnvironmentVariable
from src.common.env import S3_DATA_BUCKET_NAME, CALIBRATED_POTENTIAL_TABLE
from src.common.g3_any_api_service import G3AnyApiService, PaceAccomActivityRequest, DecisionVolumeComparisonRequest, \
    DecisionVolumeAnalysisRequest, DecisionAnalysisRequest, G3AnyApiGlobalService, InputProcessingRequest, \
    PropertyIdRequest
from src.common.http_authorizers.g3_auth import G3<PERSON><PERSON>
from src.common.http_service import HttpService
from src.common.s3_service import s3_service
from src.common.ucs_service import UCSConfigParam, get_calibration_percentile, \
    get_calibration_rolling_window, should_use_leading_window, should_fail_on_missing_occupancy_dates
from src.common.ups_service import ups_service, get_property_info

from src.common.utilities.DateUtil import DateUtil

class ReportsService:

    _CLIENT_ID = 3 #Hilton
    _CONCURRENCY_LIMIT = 10

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._noOfDays = 7

    def generate_report(self, client_code, property_code, g3_url, caught_up_dates: list[date], property_id) -> BytesIO:
        if isinstance(caught_up_dates[0], date):
            caught_up_dates = [c.isoformat() for c in caught_up_dates]
        dfs = []
        base_s3_path = f's3://{S3_DATA_BUCKET_NAME}/{client_code}/{property_code}'
        for d in caught_up_dates:
            caught_up_assem = s3_service.download_folder_and_concat(
                f'{base_s3_path}/{d}')
            caught_up_assem['caughtUpDate'] = pd.to_datetime(d)
            dfs.append(caught_up_assem)
        all_assem = pd.concat(dfs, ignore_index=True) if len(dfs) > 0 else pd.DataFrame()

        idp_count = dynamodb_service.fetch_idp_count(client_code=client_code, property_code=property_code,
                                                     caught_up_dates=caught_up_dates)
        idp_window = dynamodb_service.fetch_idp_window(client_code=client_code, property_code=property_code,
                                                       caught_up_dates=caught_up_dates)
        latest_potentials = dynamodb_service.fetch_calibrated_potentials_df(CALIBRATED_POTENTIAL_TABLE,
                                                                            client_code=client_code,
                                                                            property_code=property_code)
        objects = [s3_service.get_file_name(s3_service.get_parent(i)) for i in
                   s3_service.list_objects(f'{base_s3_path}/calibration', suffix='assemblage.csv')]
        latest_caught_up_date = max([date.fromisoformat(o) for o in objects])
        latest_calibration_assemblage = s3_service.fetch_file(
            f'{base_s3_path}/calibration/{latest_caught_up_date}/assemblage.csv')
        capacity = self.fetch_g3_data(g3_url, property_id, latest_caught_up_date)
        ucs_config = self.fetch_ucs_config(client_code, property_code)
        output = BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            all_assem.to_excel(writer, index=False, sheet_name="evaluationAssemblages")
            idp_count.to_excel(writer, index=False, sheet_name="idpCount")
            idp_window.to_excel(writer, index=False, sheet_name="idpWindow")
            latest_potentials.to_excel(writer, index=False, sheet_name="latestPotential")
            latest_calibration_assemblage.to_excel(writer, index=False,
                                                   sheet_name="latestCalibPotAssemb")
            capacity.to_excel(writer, index=False, sheet_name="capacityInfo")
            ucs_config.to_excel(writer, index=False, sheet_name="ucsConfig")
        output.seek(0)
        return output

    def fetch_g3_data(self, g3_url, property_id, latest_caught_up_date):
        http_service = HttpService(g3_url, authorizer=G3Auth())
        return (G3AnyApiService(http_service, property_id)
                .fetch(PaceAccomActivityRequest(latest_caught_up_date - timedelta(days=210), latest_caught_up_date)))

    def fetch_ucs_config(self, client_code, property_code):
        return pd.DataFrame({
            UCSConfigParam.CALIBRATION_PERCENTILE.value: [get_calibration_percentile(client_code, property_code)],
            UCSConfigParam.CALIBRATION_ROLLING_WINDOW.value: [
                get_calibration_rolling_window(client_code, property_code)],
            UCSConfigParam.CALIBRATION_USE_LEADING_WINDOW.value: [
                should_use_leading_window(client_code, property_code)],
            UCSConfigParam.FAIL_ON_MISSING_OCCUPANCY_DATES.value: [should_fail_on_missing_occupancy_dates(client_code,
                                                                                                          property_code)]
        })

    def fetch_reports(self, report: DecisionAnalysisReports):
        client_codes = report.client_codes_to_include
        property_codes = report.property_codes_to_include
        property_info = self.get_all_global_reports(client_codes, property_codes, report.g3Url, self._CLIENT_ID, PropertyIdRequest(client_code=client_codes, property_code=property_codes))
        property_ids = property_info['propertyId'].tolist()

        decision_volume_analysis_report = self.get_all_tenant_reports(property_ids, report.g3Url, DecisionVolumeAnalysisRequest())
        decision_volume_comparison_report = self.get_all_tenant_reports(property_ids, report.g3Url, DecisionVolumeComparisonRequest())
        decision_analysis_report = self.get_all_tenant_reports(property_ids, report.g3Url, DecisionAnalysisRequest())
        input_processing_request = self.get_all_global_reports(client_codes, property_codes, report.g3Url, self._CLIENT_ID, InputProcessingRequest(client_code=client_codes, property_code=property_codes))
        property_attributes = self.get_all_ups_data(client_codes, property_codes)[['clientCode', 'propertyCode', 'timeZone', 'tier', 'brandCode', 'globalArea', 'locationType']]

        decision_analysis_report = pd.merge(decision_analysis_report, property_attributes, on=['clientCode','propertyCode'], how='inner')
        decision_analysis_report['timeInCst'] = decision_analysis_report.apply(lambda x: DateUtil.convert_to_cst(x['localDate'], x['timeZone']), axis=1)
        decision_analysis_report = decision_analysis_report[['clientCode', 'propertyCode', 'occupancyDate', 'dowName', 'DTA', 'decisionDate', 'localDate', 'timeInCst', 'decisionTypeName', 'name', 'systemNewDecision', 'systemOldDecision', 'absoluteDifference', 'absolutePercentage']]

        lowest_date = decision_analysis_report['decisionDate'].min()
        caught_up_dates = [date.strftime('%Y-%m-%d') for date in fastDateRange.fastDateRangeNDays(datetime.strptime(lowest_date, '%Y-%m-%d').date(), self._noOfDays)]

        idp_window_report = pd.concat([
            dynamodb_service.fetch_idp_window(
                client_code=report.client_codes_to_include[i],
                property_code=report.property_codes_to_include[i],
                caught_up_dates=caught_up_dates
            )
            for i in range(len(property_ids))
        ]).drop_duplicates()
        idp_window_report[['clientCode', 'propertyCode']] = idp_window_report['clientCode_propertyCode'].str.split('_', expand=True)
        idp_window_report = pd.merge(idp_window_report, property_attributes, on=['clientCode','propertyCode'], how='inner')
        if not idp_window_report.empty:

            idp_window_report['maxOccupancyDate'] = pd.to_datetime(idp_window_report['maxOccupancyDate'])
            idp_window_report['caughtUpDate'] = pd.to_datetime(idp_window_report['caughtUpDate'])
            idp_window_report['IDPWindowRecommended'] = (idp_window_report['maxOccupancyDate'] - idp_window_report['caughtUpDate']).dt.days
            idp_window_report['caughtUpDate'] = [date.strftime('%Y-%m-%d') for date in idp_window_report['caughtUpDate']]
            idp_window_report['evaluationTime'] = idp_window_report.apply(lambda x: DateUtil.convert_from_cst(x['evaluationTime'], x['timeZone']), axis=1)
            idp_window_report = idp_window_report.rename(columns={'clientCode_propertyCode' : 'InnCode', 'caughtUpDate' : 'businessDate'})
            idp_window_report = idp_window_report[['InnCode', 'businessDate', 'evaluationTime', 'IDPWindowRecommended']]

        input_processing_request['preparedDate'] = pd.to_datetime(input_processing_request['preparedDate'])
        input_processing_request['preparedDate'] = input_processing_request['preparedDate'] + timedelta(days=1)
        input_processing_request['preparedDate'] = input_processing_request['preparedDate'].dt.strftime('%Y-%m-%d %H:%M:%S')
        input_processing_request = input_processing_request.rename(columns={'preparedDate' : 'localDate', 'inputType' : 'processingType'})
        input_processing_request['processingType'] = input_processing_request['processingType'].replace({'CDP':'IDP', 'CDP_ON_DEMAND':'IDP_RTO'})
        decision_volume_analysis_report = pd.merge(decision_volume_analysis_report, input_processing_request, on=['clientCode','propertyCode','localDate'], how='right')
        decision_analysis_report = pd.merge(decision_analysis_report, input_processing_request, on=['clientCode','propertyCode','localDate'], how='right')

        decision_volume_analysis_report = pd.merge(decision_volume_analysis_report, property_attributes, on=['clientCode','propertyCode'], how='inner')
        decision_volume_analysis_report['localDate'] = pd.to_datetime(decision_volume_analysis_report['localDate'])
        decision_volume_analysis_report['businessDate'] = decision_volume_analysis_report['businessDate'].fillna(decision_volume_analysis_report['localDate'].dt.date).astype(str)
        decision_volume_analysis_report['decisionTypeName'] = decision_volume_analysis_report['decisionTypeName'].fillna('DailyBar')
        decision_volume_analysis_report['countBarDecisions'] = decision_volume_analysis_report['countBarDecisions'].fillna(0)
        decision_volume_analysis_report['averageAbsoluteVariance'] = decision_volume_analysis_report['averageAbsoluteVariance'].fillna(0)
        decision_volume_analysis_report['serverTime'] = decision_volume_analysis_report.apply(lambda x: DateUtil.convert_from_cst(x['localDate'], x['timeZone']),axis=1)
        decision_volume_analysis_report['localDate'] = decision_volume_analysis_report['localDate'].dt.strftime('%Y-%m-%d %H:%M:%S')

        decision_volume_analysis_report = decision_volume_analysis_report[['clientCode','propertyCode','businessDate','localDate','serverTime','decisionTypeName','countBarDecisions','averageAbsoluteVariance','processingType']]


        output = BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            decision_volume_analysis_report.to_excel(writer, index=False, sheet_name="decisionVolumeAnalysis")
            decision_volume_comparison_report.to_excel(writer, index=False, sheet_name="decisionVolumeComparison")
            decision_analysis_report.to_excel(writer, index=False, sheet_name="decisionAnalysis")
            idp_window_report.to_excel(writer, index=False, sheet_name='idpTriggeredDataAnalysis')
            property_attributes.to_excel(writer, index=False, sheet_name='propertyAttributes')
        output.seek(0)
        return output

    def fetch_decision_volume_comparison_report(self, g3_url, property_id):
        http_service = HttpService(g3_url, authorizer=G3Auth())
        return (G3AnyApiService(http_service, property_id)).fetch(DecisionVolumeComparisonRequest())

    def fetch_decision_volume_analysis_report(self, g3_url, property_id):
        http_service = HttpService(g3_url, authorizer=G3Auth())
        return (G3AnyApiService(http_service, property_id)).fetch(DecisionVolumeAnalysisRequest())

    def fetch_decision_analysis_report(self, g3_url, property_id):
        http_service = HttpService(g3_url, authorizer=G3Auth())
        return (G3AnyApiService(http_service, property_id)).fetch(DecisionAnalysisRequest())

    def fetch_input_processing_request(self, g3_url, client_id, client_code, property_code):
        http_service = HttpService(g3_url, authorizer=G3Auth())
        return (G3AnyApiGlobalService(http_service, client_id, client_code)).fetch(InputProcessingRequest(client_code, property_code))

    def fetch_property_id_request(self, g3_url, client_id, client_code, property_code):
        http_service = HttpService(g3_url, authorizer=G3Auth())
        return (G3AnyApiGlobalService(http_service, client_id, client_code)).fetch(PropertyIdRequest(client_code, property_code))

    def fetch_property_attributes(self, client_code: str, property_code: str) -> list[PropertyInfo]:
        return get_property_info(client_code, property_code)

    def get_all_ups_data(self, client_codes, property_codes):
        async def fetch_one_property_info(client_code, property_code, semaphore):
            async with semaphore:
                return await asyncio.to_thread(self.fetch_property_attributes, client_code, property_code)

        async def batch_fetch_reports():
            semaphore = asyncio.Semaphore(self._CONCURRENCY_LIMIT)
            tasks = [fetch_one_property_info(client_code, property_code, semaphore) for client_code, property_code in zip(client_codes, property_codes)]
            results = await asyncio.gather(*tasks)
            df = pd.DataFrame([result.model_dump(by_alias=True) for result in results])
            return df

        return asyncio.run(batch_fetch_reports())


    def get_all_tenant_reports(self, property_ids, g3_url, request: DecisionVolumeAnalysisRequest | DecisionVolumeComparisonRequest | DecisionAnalysisRequest):
        async def fetch_one_report(property_id, semaphore, request):
            async with semaphore:
                if isinstance(request, DecisionVolumeAnalysisRequest):
                    return await asyncio.to_thread(self.fetch_decision_volume_analysis_report, g3_url, property_id)
                elif isinstance(request, DecisionVolumeComparisonRequest):
                    return await asyncio.to_thread(self.fetch_decision_volume_comparison_report, g3_url, property_id)
                elif isinstance(request, DecisionAnalysisRequest):
                    return await asyncio.to_thread(self.fetch_decision_analysis_report, g3_url, property_id)

        async def batch_fetch_reports():
            semaphore = asyncio.Semaphore(self._CONCURRENCY_LIMIT)
            tasks = [fetch_one_report(pid, semaphore, request) for pid in property_ids]
            results = await asyncio.gather(*tasks)
            return pd.concat(results).drop_duplicates()

        return asyncio.run(batch_fetch_reports())

    def get_all_global_reports(self, client_codes, property_codes, g3_url, client_id, request: InputProcessingRequest | PropertyIdRequest):
        async def fetch_one_report(client_code, property_code, client_id, semaphore, request):
            async with semaphore:
                if isinstance(request, InputProcessingRequest):
                    return await asyncio.to_thread(self.fetch_input_processing_request, g3_url, client_id, client_code, property_code)
                elif isinstance(request, PropertyIdRequest):
                    return await asyncio.to_thread(self.fetch_property_id_request, g3_url, client_id, client_code, property_code)

        async def batch_fetch_reports():
            semaphore = asyncio.Semaphore(self._CONCURRENCY_LIMIT)
            tasks = [fetch_one_report(client_code, property_code, client_id, semaphore, request)
                     for client_code in client_codes
                     for property_code in property_codes]
            results = await asyncio.gather(*tasks)
            return pd.concat(results).drop_duplicates()

        return asyncio.run(batch_fetch_reports())


reports_service = ReportsService()

if __name__ == '__main__':
    reports_service.get_all_ups_data(['Hilton', 'Hilton'], ['REKCU', 'TPANH'])