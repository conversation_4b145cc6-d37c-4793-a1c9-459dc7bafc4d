import json

from awswrangler import secretsmanager

from src.common.enums.env_var import EnvironmentVariable
from src.common.env import get_value


def get_fds_auth_secret():
    secret_manager_name = get_value(EnvironmentVariable.SECRET_MANAGER_NAME, 'g3_secret')
    secrets: dict = json.loads(secretsmanager.get_secret(secret_manager_name))
    return secrets['fds_auth_token']


def get_g3_auth_secret():
    g3_auth_secret_name = get_value(EnvironmentVariable.SECRET_MANAGER_NAME, 'g3_secret')
    g3_auth_secret_key = 'g3_auth_token'
    secrets: dict = json.loads(secretsmanager.get_secret(g3_auth_secret_name))
    return secrets[g3_auth_secret_key]
