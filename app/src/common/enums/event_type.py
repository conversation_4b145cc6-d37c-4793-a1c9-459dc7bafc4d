from enum import Enum
from typing import Union

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

from src.common.dto.DecisionAnalysisReports import DecisionAnalysisReports
from src.common.dto.GenerateAssemblageMrRequestSource import GenerateAssemblageMrRequestSource
from src.common.dto.calib_request import CalibrationRequestSource
from src.common.dto.evaluate_request import EvaluationRequestSource
from src.common.dto.revise_potential_request import RevisePotentialRequestSource

SupportedEventSources = Union[CalibrationRequestSource, EvaluationRequestSource, GenerateAssemblageMrRequestSource, RevisePotentialRequestSource, DecisionAnalysisReports]


class EventTypeDetail(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    event_name: str
    event_type: type[SupportedEventSources]

class EventType(Enum):

    DYN_OPT_EVALUATE_FOR_IDP = EventTypeDetail(event_name="DYN_OPT_EVALUATE_FOR_IDP",
                                               event_type=EvaluationRequestSource)
    DYN_OPT_POTENTIAL_CALIBRATION = EventTypeDetail(event_name="DYN_OPT_POTENTIAL_CALIBRATION",
                                                    event_type=CalibrationRequestSource)
    GENERATE_ASSEMBLAGE_MR = EventTypeDetail(event_name="GENERATE_ASSEMBLAGE_MR",
                                             event_type=GenerateAssemblageMrRequestSource)
    DYN_OPT_REVISE_POTENTIAL = EventTypeDetail(event_name="DYN_OPT_REVISE_POTENTIAL",
                                               event_type=RevisePotentialRequestSource)
    GENERATE_DECISION_ANALYSIS_REPORTS = EventTypeDetail(event_name="GENERATE_DECISION_ANALYSIS_REPORTS", 
                                                         event_type=DecisionAnalysisReports)
