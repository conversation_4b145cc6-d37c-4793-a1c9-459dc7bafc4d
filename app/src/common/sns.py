from datetime import datetime, timezone

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel


class SNSEvent(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    source_system: str = "dyn-opt"
    scope: str
    version: str = "1"
    event_date_time: datetime | str = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
    event_source: str
    event_type: str

    def get_message_attributes(self):
        return {"sourceSystem": {"DataType": "String", "StringValue": self.source_system},
                "version": {"DataType": "String", "StringValue": self.version},
                "eventType": {"DataType": "String", "StringValue": self.event_type},
                "scope": {"DataType": "String", "StringValue": self.scope}}
