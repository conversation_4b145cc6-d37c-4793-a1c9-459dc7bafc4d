import logging

from src.common.enums.env_var import EnvironmentVariable
from src.common.env import get_value
from src.common.s3_service import s3_service

logger = logging.getLogger(__name__)


def log_df(client_code, property_code, file_name, df):
    bucket = get_value(EnvironmentVariable.S3_DATA_BUCKET_NAME, 'dev-dyn-opt-data')
    save_at = f's3://{bucket}/{client_code}/{property_code}/{file_name}'
    s3_service.save_df(save_at, df)


def save_df_in_parent(s3_path, file_name, df):
    s3_service.save_df(f'{s3_service.get_parent(s3_path)}/{file_name}', df)


def upload_pickle(s3_path, file_name, file_like):
    s3_service.upload_file(path=f'{s3_service.get_parent(s3_path)}/{file_name}',
                           file_like=file_like)
