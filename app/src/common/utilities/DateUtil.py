from datetime import datetime
import pytz

Y_M_D = '%Y-%m-%d'

Y_M_D_H_M_S = '%Y-%m-%d %H:%M:%S'


class DateUtil:
    @staticmethod
    def convert_to_cst(local_dt, time_zone):

        local_dt = datetime.strptime(local_dt, '%Y-%m-%d %H:%M:%S')
        local_tz = pytz.timezone(time_zone)
        local_dt = local_tz.localize(local_dt)
        cst_tz = pytz.timezone('US/Central')
        cst_dt = local_dt.astimezone(cst_tz)
        return cst_dt.strftime('%Y-%m-%d %H:%M:%S')

    @staticmethod
    def convert_from_cst(cst_dt, time_zone):

        cst_tz = pytz.timezone('US/Central')
        cst_dt = cst_tz.localize(cst_dt)
        local_tz = pytz.timezone(time_zone)
        local_dt = cst_dt.astimezone(local_tz)
        return local_dt.strftime('%Y-%m-%d %H:%M:%S')

    @staticmethod
    def get_current_date_for_timezone(time_zone):

        tz = pytz.timezone(time_zone)
        current_date = datetime.now(tz)
        return current_date.strftime(Y_M_D)

    @staticmethod
    def convert_to_gmt(local_dt, time_zone):

        local_dt = DateUtil.str_to_dttm(local_dt,Y_M_D_H_M_S)
        local_tz = pytz.timezone(time_zone)
        local_dt = local_tz.localize(local_dt)
        cst_tz = pytz.timezone('GMT')
        cst_dt = local_dt.astimezone(cst_tz)
        return cst_dt.strftime(Y_M_D_H_M_S)

    @staticmethod
    def str_to_dttm(local_dt,date_format):
        local_dt = datetime.strptime(local_dt, date_format)
        return local_dt

    @staticmethod
    def str_to_dt(local_dt,date_format=Y_M_D):
        return DateUtil.str_to_dttm(local_dt, date_format).date()




