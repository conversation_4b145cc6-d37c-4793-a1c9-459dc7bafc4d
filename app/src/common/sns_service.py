import json
import logging

import boto3

from src.common.dto.evaluation_result import EvaluationR<PERSON>ult
from src.common.dto.evaluation_sns_message import EvaluationSNSMessage
from src.common.enums.env_var import EnvironmentVariable
from src.common.env import get_value
from src.common.pydantic_util import to_json_string
from src.common.sns import SNSEvent


class SNSService:
    SIGNIFICANT_SOLDS_DRIFT_DETECTED = 'SIGNIFICANT_SOLDS_DRIFT_DETECTED'

    def __init__(self, sns_client=boto3.client('sns',
                                               endpoint_url=get_value(EnvironmentVariable.AWS_SNS_ENDPOINT_URL, None))):
        self.sns_client = sns_client
        self.topic_arn = get_value(EnvironmentVariable.FDS_UEN_ARN, "arn")
        self.env = 'prod'
        self.logger = logging.getLogger(__name__)

    def send_trigger_rms_optimization(self, client_code, property_code, evaluation_result: EvaluationResult, cap_date,
                                      evaluation_time):
        event_source = EvaluationSNSMessage(client_code=client_code, property_code=property_code,
                                            should_optimize=evaluation_result.should_optimize,
                                            max_occupancy_date=evaluation_result.max_occupancy_date,
                                            evaluation_time=evaluation_time, caught_up_date=cap_date,
                                            rc_to_occupancy_date_mapping=evaluation_result.rc_to_occupancy_date_mapping)
        self.send_event(event_source, self.SIGNIFICANT_SOLDS_DRIFT_DETECTED)

    def send_event(self, event_source, event_name):
        sns_event = SNSEvent(scope=self.env, event_type=event_name, event_source=to_json_string(event_source))
        self.logger.info(f"publishing event {sns_event}")
        self.sns_client.publish(
            TopicArn=self.topic_arn,
            Message=to_json_string(sns_event),
            MessageAttributes=sns_event.get_message_attributes()
        )

    def send_raw(self, event):
        self.logger.info(f"publishing event {event}")
        attr = {"sourceSystem": {"DataType": "String", "StringValue": event['sourceSystem']},
                "version": {"DataType": "String", "StringValue": event['version']},
                "eventType": {"DataType": "String", "StringValue": event['eventType']},
                "scope": {"DataType": "String", "StringValue": event['scope']}}
        event['eventSource'] = json.dumps(event['eventSource'])
        self.sns_client.publish(
            TopicArn=self.topic_arn,
            Message=json.dumps(event),
            MessageAttributes=attr
        )
