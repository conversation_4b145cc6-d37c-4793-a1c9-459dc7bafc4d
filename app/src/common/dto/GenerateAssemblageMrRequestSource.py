from datetime import date
from typing import override, Optional

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

from src.common.base_classes.property_ctx_settable import PropertyContextSettable


class GenerateAssemblageMrRequestSource(BaseModel, PropertyContextSettable):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    client_code: str
    property_code: str
    property_id: int
    g3Url: str
    caught_up_dates: list[date]
    report_id: Optional[str] = ""

    @override
    def get_property_code(self):
        return self.property_code

    @override
    def get_client_code(self):
        return self.client_code
