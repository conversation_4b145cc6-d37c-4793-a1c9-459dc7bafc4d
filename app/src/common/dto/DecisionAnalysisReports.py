from typing import override, Optional

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

class DecisionAnalysisReports(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    client_codes_to_include: list[str]
    property_codes_to_include: list[str]
    g3Url: str
    report_id: Optional[str] = ""

    def get_property_codes(self):
        return self.property_codes_to_include

    def get_client_codes(self):
        return self.client_codes_to_include