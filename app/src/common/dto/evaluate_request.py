from typing import override

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

from src.common.base_classes.property_ctx_settable import PropertyContextSettable
from src.common.dto.evaluation_dynamodb_input import EvaluationDynamodbTableInput


class EvaluationRequestSource(BaseModel, PropertyContextSettable):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    client_code: str
    property_code: str
    request_context: EvaluationDynamodbTableInput

    @override
    def get_property_code(self):
        return self.property_code

    @override
    def get_client_code(self):
        return self.client_code
