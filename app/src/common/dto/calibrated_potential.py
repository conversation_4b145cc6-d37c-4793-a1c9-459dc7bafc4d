from decimal import Decimal

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel


class CalibratedPotential(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)
    accom_class_id: int
    calibrated_potential: float

    def get_dynamodb_record(self, client_code, property_code):
        return {
            'clientCode': client_code,
            'propertyCode': property_code,
            'accomClassId': self.accom_class_id,
            'clientCode_propertyCode': self.get_partition_key(client_code, property_code),
            'metricType': 1,
            'accomClassId_metricType': f'{self.accom_class_id}_1',
            'calibratedPotential': Decimal(str(self.calibrated_potential))
        }

    @staticmethod
    def get_partition_key(client_code, property_code):
        return f'{client_code}_{property_code}'

