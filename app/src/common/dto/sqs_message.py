import json
from typing import Union, Optional

from aws_lambda_powertools.utilities.data_classes.sqs_event import SQSRecordAttributes
from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

from src.common.dto.sqs_message_body import SQSMessageBody
from src.common.pydantic_util import from_dict


class SQSMessage(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True, arbitrary_types_allowed=True)

    message_id: str
    body: SQSMessageBody
    receipt_handle: str
    attributes: Optional[SQSRecordAttributes] = {}

    @staticmethod
    def generate_model(r) :
        body = json.loads(r['Body'])
        if 'Message' in body.keys():
            body = json.loads(body['Message'])
        body['eventSource'] = json.loads(body['eventSource'])
        b = from_dict(SQSMessageBody, body)
        if 'Attributes' in body.keys():
            return SQSMessage(message_id=r['MessageId'], body=b, receipt_handle=r['ReceiptHandle'], attributes=r['Attributes'])
        return SQSMessage(message_id=r['MessageId'], body=b, receipt_handle=r['ReceiptHandle'])
