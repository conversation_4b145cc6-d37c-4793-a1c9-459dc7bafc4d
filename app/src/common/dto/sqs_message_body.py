from datetime import datetime
from typing import override, Self, Any

from pydantic import BaseModel, ConfigDict, model_validator, field_validator
from pydantic.alias_generators import to_camel

from src.common.base_classes.property_ctx_settable import PropertyContextSettable
from src.common.enums.event_type import EventType
from src.common.pydantic_util import from_dict


class SQSMessageBody(BaseModel, PropertyContextSettable):

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    source_system: str
    event_type: EventType | str
    version: int
    event_date_time: datetime
    event_source: dict[str, Any]

    @field_validator('event_type', mode='before')
    @classmethod
    def event_type_validator(cls, v):
        try:
            status = EventType[v]
        except Exception:
            raise ValueError(f"{v} must of type EventType")
        return status

    @override
    def get_property_code(self):
        return self.event_source.property_code

    @override
    def get_client_code(self):
        return self.event_source.client_code

    @model_validator(mode='after')
    @classmethod
    def event_source_validator(cls, v: Self):
        deserialized = from_dict(v.event_type.value.event_type, v.event_source)
        v.event_source = deserialized
        return v
