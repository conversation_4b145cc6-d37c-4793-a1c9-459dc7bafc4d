from pydantic import BaseModel, field_validator, ValidationInfo, ConfigDict
from pydantic.alias_generators import to_camel

from src.common.dto.calibrated_potential import CalibratedPotential


class EvaluationS3FileInput(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    delta_occ_solds: str
    reference_rate: str
    delta_lrv: str
    min_data_requirement: int
    max_data_limit: int
    calibrated_potential: list[CalibratedPotential]


    @field_validator('delta_occ_solds', 'reference_rate', 'delta_lrv')
    @classmethod
    def validate(cls, value:str, info: ValidationInfo) -> str:
        __S3_PREFIX__ = 's3://'
        if __S3_PREFIX__ in value:
            return value
        raise ValueError(f"Not a valid s3 path {info.field_name} : {value}")

