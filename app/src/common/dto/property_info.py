from typing import Optional


from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel


class PropertyInfo(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)
    client_code: Optional[str]
    property_code: Optional[str]
    time_zone: str
    tier: str
    brand_code: str
    global_area: str
    location_type: str
    g3_host_url: str
