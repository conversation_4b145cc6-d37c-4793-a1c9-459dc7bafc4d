from datetime import date
from typing import Dict

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel


class EvaluationSNSMessage(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    client_code: str
    property_code: str
    should_optimize: bool
    max_occupancy_date: date
    evaluation_time: str
    caught_up_date: str
    rc_to_occupancy_date_mapping: Dict[int, date]
