from typing import override

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

from src.common.base_classes.property_ctx_settable import PropertyContextSettable
from src.common.dto.calibration_s3_file_input import CalibrationS3FileInput


class CalibrationRequestSource(BaseModel, PropertyContextSettable):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    client_code: str
    property_code: str
    destination_table: str
    request_context: CalibrationS3FileInput

    @override
    def get_property_code(self):
        return self.property_code

    @override
    def get_client_code(self):
        return self.client_code
