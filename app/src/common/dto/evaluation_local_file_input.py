from pydantic import BaseModel, FilePath, ConfigDict
from pydantic.alias_generators import to_camel

from src.common.dto.calibrated_potential import CalibratedPotential


class EvaluationLocalFileInput(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    delta_occ_solds: FilePath
    reference_rate: FilePath
    delta_lrv: FilePath
    min_data_requirement: int
    max_data_limit: int
    calibrated_potential: list[CalibratedPotential] | FilePath
