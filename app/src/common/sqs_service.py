import logging

import boto3

from src.common.dto.sqs_message import SQSMessage


class SQSService:

    DEFAULT_VISIBILITY_TIMEOUT = 600
    DEFAULT_NUMBER_OF_MESSAGES = 10

    def __init__(self, sqs_client = boto3.client('sqs')):
        self.logger = logging.getLogger(__name__)
        self.sqs_client = sqs_client

    def receive_message(self, queue_url) -> list[SQSMessage]:
        msg = self.sqs_client.receive_message(QueueUrl=queue_url, MaxNumberOfMessages=self.DEFAULT_NUMBER_OF_MESSAGES,
                                              VisibilityTimeout=self.DEFAULT_VISIBILITY_TIMEOUT)
        if 'Messages' in msg:
            self.logger.info(f'received on {queue_url} {msg['Messages']}')
            return [SQSMessage.generate_model(r) for r in msg['Messages']]
        return []

    def delete_message(self, queue_url, msg: SQSMessage):
        self.logger.info(f"Deleting message {msg}")
        self.sqs_client.delete_message(QueueUrl=queue_url, ReceiptHandle=msg.receipt_handle)


