import logging
import pickle
from datetime import date, timedelta, datetime
from io import BytesIO
from typing import Callable
import re

import boto3
import pandas as pd

from src.algorithm.calibration_service import run_calibration, compute_new_percentile, \
    revise_threshold
from src.algorithm.dynamic_optimization_package.CalibrationAdjustment import get_new_threshold_from_fun
from src.algorithm.dynamic_optimization_package.CommonDynamicOptElements import CommonDOElements
from src.algorithm.dynamic_optimization_package.RevisionPackage.RevisionProcess import RevisionProcess
from src.algorithm.dynamic_optimization_package.daily_median_lrv_change_calculator import calculate_lrv_change
from src.common import file_logger_service
from src.common.dto.calib_request import CalibrationRequestSource
from src.common.dto.calibrated_potential import CalibratedPotential
from src.common.dto.calibration_local_file_input import CalibrationLocalFileInput
from src.common.dto.calibration_s3_file_input import CalibrationS3FileInput
from src.common.dynamodb_service import DynamoDBService, dynamodb_service
from src.common.env import S3_DATA_BUCKET_NAME, CALIBRATED_POTENTIAL_TABLE
from src.common.g3_any_api_service import G3AnyApiGlobalService, InputProcessingRequest
from src.common.http_authorizers.g3_auth import G3Auth
from src.common.http_service import HttpService
from src.common.s3_service import s3_service
from src.common.ucs_service import (get_calibration_rolling_window, should_use_leading_window, \
                                    should_fail_on_missing_occupancy_dates, get_pressure_floor,
                                    get_calibration_percentile, ucs_service, UCSConfigParam, \
                                    get_user_waste_weight, get_waste_threshold, get_regret_threshold,
                                    get_waste_regret_min_data, \
                                    get_min_shift_delta_lrv_threshold, get_max_shift_lrv_threshold, get_simulation_type,
                                    get_current_avg_target_opts,
                                    should_ovr_full_window_update, should_include_scheduled_idp,
                                    should_prefer_observed_avg_opts, \
                                    get_tolerable_binary_search_error,
                                    get_total_scheduled_idp_count, get_max_idp_recommendation_cnt)
from src.common.ups_service import ups_service, get_property_host_url
from src.common.utilities.DateUtil import DateUtil
from src.log_config.setup import setup_logger


class CalibrationService:
    TEST_PERCENTILE = 50

    def __init__(self, _dynamodb_service=DynamoDBService()):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.dynamodb_service = _dynamodb_service
        self.POTENTIAL_MODE = 'slidingwindow'
        self.rp = RevisionProcess(pressure_col='pressure', potential_col='potential', day_col='occupancyDate'
                                  , eval_time_col='evaluationTime', accom_class_col='accomClassId', lrv_col='lrv', price_col='price'
                                  , delta_lrv_col='deltaLRV', calib_potential_col='calibratedPotential', inc_solds_col='incSolds'
                                  , caught_up_date_col='caughtUpDate', lead_time_col='leadTime', avail_cap_col='availableCapacity',
                                  delta_solds_col='deltaSolds', ceil_col='ceilingValue', ref_rate_col='price')

    def run_calibration_from_s3(self, s3_path_holder: CalibrationS3FileInput, percentile=75.,
                                use_leading_window=False, fail_on_missing_occ_dates=True, rolling_window=21,
                                pressure_floor=0.001) -> tuple[list[CalibratedPotential], pd.DataFrame, pd.DataFrame]:
        self.logger.info(f"Calibrating {s3_path_holder}")
        try:
            file_reader = s3_service.fetch_file

            return run_calibration(file_reader=file_reader
                                   , delta_occ_solds=s3_path_holder.delta_occ_solds
                                   , reference_rate=s3_path_holder.reference_rate
                                   , delta_lrv=s3_path_holder.delta_lrv
                                   , window_size=rolling_window
                                   , min_data_req=s3_path_holder.min_data_requirement
                                   , percentile=percentile
                                   , use_leading_window=use_leading_window
                                   , fail_on_missing_occ_dates=fail_on_missing_occ_dates
                                   , pressure_floor=pressure_floor)
        except Exception as e:
            self.logger.exception(msg=f"Cannot calibrate with {s3_path_holder} due to {e}")
            raise e

    def run_full_calibration_from_s3_save_results(self, source_request: CalibrationRequestSource):
        client_code = source_request.client_code
        property_code = source_request.property_code
        cur_percentile = get_calibration_percentile(client_code, property_code)
        today = date.today().isoformat()
        try:
            # new_percentile = self.get_calibrated_percentile(client_code, property_code, cur_percentile)
            new_percentile = cur_percentile
            self.logger.info(f"Calibrated Percentile: {new_percentile}")

        except Exception as e:
            self.logger.exception(
                msg=f"Failed waste/regret based calibration. Continue to use prev percentile  due to {e}")
            new_percentile = cur_percentile
        self.calibrate_and_save_last_calib_date(client_code, new_percentile, property_code, source_request, today)

    def calibrate_and_save_last_calib_date(self, client_code, new_percentile, property_code, source_request,
                                           today: str):
        potentials, debug_frame, final_total_pressure = (
            self.run_calibration_from_s3(source_request.request_context,
                                         new_percentile,
                                         should_use_leading_window(client_code, property_code),
                                         should_fail_on_missing_occupancy_dates(client_code, property_code),
                                         get_calibration_rolling_window(client_code, property_code),
                                         get_pressure_floor(client_code, property_code)))
        self.logger.info(f"Saving results {potentials}")
        file_logger_service.save_df_in_parent(source_request.request_context.delta_occ_solds, 'assemblage.csv',
                                              debug_frame)
        file_logger_service.upload_pickle(source_request.request_context.delta_occ_solds,
                                          'quantile_function.pkl',
                                          BytesIO(pickle.dumps(final_total_pressure)))
        self.dynamodb_service.update_potentials(source_request.destination_table, potentials,
                                                client_code, property_code)

        self.update_last_calib_date(client_code=client_code, property_code=property_code, caught_up_date=today, final_total_pressure=final_total_pressure)
        self.dynamodb_service.update_last_threshold_revision_date(client_code=client_code, property_code=property_code, caught_up_date=today)

    def update_last_calib_date(self, client_code: str, property_code: str, caught_up_date: str, final_total_pressure: dict):
        invalid_rc = [
            rc for rc, potential in final_total_pressure.items()
            if potential(self.TEST_PERCENTILE) == CommonDOElements.__insufficientDataThresholdPotential__
        ]
        if len(invalid_rc) != len(final_total_pressure):
            self.dynamodb_service.update_last_calib_date(client_code=client_code, property_code=property_code,caught_up_date=caught_up_date)
            self.logger.info(f"Updated last calib date for {client_code} {property_code}, invalid potential for {invalid_rc}")
        else:
            self.logger.info(f"Last Calib Date update skipped for {client_code} {property_code}: invalid potential for {invalid_rc}")

    # Called weekly to adjust the percentile and revise the threshold
    def run_threshold_revision(self, client_code: str, property_code: str):
        self.logger.debug("Running threshold revision")
        idp_window = dynamodb_service.fetch_idp_window(client_code=client_code, property_code=property_code)
        change_lrv = dynamodb_service.fetch_median_abs_lrv_change(client_code=client_code, property_code=property_code)
        lrv_change_df = calculate_lrv_change(change_lrv, idp_window)
        calibration_folder = f's3://{S3_DATA_BUCKET_NAME}/{client_code}/{property_code}/calibration'
        objects = [s3_service.get_file_name(s3_service.get_parent(i)) for i in
                   s3_service.list_objects(calibration_folder, suffix='assemblage.csv')]
        latest_caught_up_date = max([date.fromisoformat(o) for o in objects])
        thresh_fun: dict[int, Callable[[float], float]] = \
            pickle.load(
                s3_service.donwload_file_like(f'{calibration_folder}/{latest_caught_up_date}/quantile_function.pkl'))
        try:
            user_waste_weight = get_user_waste_weight(client_code=client_code, property_code=property_code)
            waste_threshold = get_waste_threshold(client_code=client_code, property_code=property_code)
            regret_threshold = get_regret_threshold(client_code=client_code, property_code=property_code)
            waste_regret_min_data_req = get_waste_regret_min_data(client_code=client_code, property_code=property_code)
            prev_calibrated_percentile = get_calibration_percentile(client_code, property_code)
            new_percentile, potentials = revise_threshold(waste_regret_df=lrv_change_df,
                                                          waste_threshold=waste_threshold,
                                                          regret_threshold=regret_threshold,
                                                          current_calib_percentile=prev_calibrated_percentile,
                                                          user_waste_weight=user_waste_weight,
                                                          min_waste_regret_sum=waste_regret_min_data_req,
                                                          threshold_func_by_rc=thresh_fun)
        except Exception as e:
            self.logger.exception(msg=f"Potential Revision failed  due to {e}")
            return
        self.logger.info(f"Calibrated Percentile: {new_percentile}")
        today = date.today().isoformat()
        ucs_service.update_param(client_code=client_code, property_code=property_code,
                                 param_name=UCSConfigParam.CALIBRATION_PERCENTILE.get_full_name(),
                                 new_value=new_percentile)
        self.dynamodb_service.update_potentials(CALIBRATED_POTENTIAL_TABLE, potentials,
                                                client_code, property_code)
        self.dynamodb_service.update_last_threshold_revision_date(client_code=client_code, property_code=property_code,
                                                                  caught_up_date=today)

    def get_potentials(self, calib_percentile: float, threshold_func: dict[int, Callable[[float], float]]) -> list[CalibratedPotential]:
        return [CalibratedPotential(accom_class_id=rc,
                                    calibrated_potential=get_new_threshold_from_fun(calib_percentile,threshold_fun))
                for rc, threshold_fun in threshold_func.items()]

    def get_latest_quantile_function(self, client_code: str, property_code: str):
        calibration_folder = f's3://{S3_DATA_BUCKET_NAME}/{client_code}/{property_code}/calibration'
        objects = [s3_service.get_file_name(s3_service.get_parent(i)) for i in
                   s3_service.list_objects(calibration_folder, suffix='assemblage.csv')]
        latest_caught_up_date = max([date.fromisoformat(o) for o in objects])
        thresh_fun: dict[int, Callable[[float], float]] = \
            pickle.load(
                s3_service.donwload_file_like(f'{calibration_folder}/{latest_caught_up_date}/quantile_function.pkl'))
        return thresh_fun

    def get_latest_quantile_functions(self, client_code: str, property_code: str) -> dict[datetime, dict[int, Callable[[float], float]]]:
        s3 = boto3.client('s3')
        bucket_name = S3_DATA_BUCKET_NAME
        prefix = f'{client_code}/{property_code}/calibration/'

        now = datetime.now()
        seven_days_ago = now - timedelta(days=7)

        pattern = re.compile(rf"{re.escape(prefix)}(\d{{4}}-\d{{2}}-\d{{2}})/quantile_function\.pkl")

        paginator = s3.get_paginator('list_objects_v2')
        pages = paginator.paginate(Bucket=bucket_name, Prefix=prefix)

        candidates = []

        for page in pages:
            for obj in page.get('Contents', []):
                key = obj['Key']
                last_modified = obj['LastModified']
                match = pattern.search(key)
                if match:
                    folder_date = datetime.strptime(match.group(1), "%Y-%m-%d")
                    candidates.append((folder_date, last_modified, key))

        if not candidates:
            raise ValueError("No quantile_function.pkl files found in calibration folders.")

        # Sort by date
        candidates.sort(key=lambda x: x[0])  # x[0] is folder_date

        # Select dates within 7 days
        in_window = [c for c in candidates if c[0] > seven_days_ago]
        fallback = [c for c in candidates if c[0] <= seven_days_ago]

        target = []
        if fallback:
            target.append(max(fallback, key=lambda x: x[0]))  # pick latest before window
        target.extend(in_window)

        # Remove duplicates (e.g., if fallback is already in in_window)
        seen_keys = set()
        final_targets = []
        for c in target:
            if c[2] not in seen_keys:
                seen_keys.add(c[2])
                final_targets.append(c)

        quantile_functions = {}
        for folder_date, last_modified, key in final_targets:
            file_path = f's3://{bucket_name}/{key}'
            thresh_fun = pickle.load(s3_service.donwload_file_like(file_path))
            quantile_functions[last_modified] = thresh_fun

        return quantile_functions

    def run_threshold_revision_v2(self, client_code: str, property_code: str):
        self.logger.info("Running threshold revision - V2")

        assem_cols = [self.rp.accom_class_col, self.rp.day_col, self.rp.lead_time_col, self.rp.avail_cap_col, self.rp.lrv_col, self.rp.delta_lrv_col, self.rp.ceil_col, self.rp.price_col, self.rp.delta_solds_col, self.rp.pressure_col, self.rp.pressure_col]

        assemblage_df = s3_service.fetch_assemblage_files(client_code=client_code, property_code=property_code, caught_up_date=date.today(), usecols=assem_cols)

        base_callback_url = get_property_host_url(client_code=client_code, property_code=property_code)
        http_service = HttpService(base_callback_url, authorizer=G3Auth())
        input_processing_df = (G3AnyApiGlobalService(http_service, client_code)).fetch(InputProcessingRequest(client_code, property_code))

        thresh_functions = self.get_latest_quantile_functions(client_code=client_code, property_code=property_code)

        eval_args = dict(
            potential_mode=self.POTENTIAL_MODE,
            window_size=get_calibration_rolling_window(client_code=client_code, property_code=property_code),
            use_leading_window=should_use_leading_window(client_code=client_code, property_code=property_code),
        )

        total_opts = get_max_idp_recommendation_cnt(client_code=client_code, property_code=property_code) - 1
        total_scheduled_opts = get_total_scheduled_idp_count(client_code=client_code, property_code=property_code)
        total_dyn_opts = total_opts - total_scheduled_opts

        old_percentile = get_calibration_percentile(client_code=client_code, property_code=property_code)

        min_date_for_eval = assemblage_df[self.rp.eval_time_col].min().date()

        new_percentile, target, observed_nopts, adjustment_type, med_max_shift_lrv, med_max_shift_delta_lrv = \
            self.rp.run_revision(
            assem=assemblage_df.copy(),
            waste_threshold=get_waste_threshold(client_code=client_code, property_code=property_code),
            regret_threshold=total_dyn_opts - 1,
            qfun=thresh_functions,
            current_calib_percentile=old_percentile,
            min_shift_delta_lrv_thresh=get_min_shift_delta_lrv_threshold(client_code=client_code, property_code=property_code),
            max_shift_lrv_thresh=get_max_shift_lrv_threshold(client_code=client_code, property_code=property_code),
            sim_type=get_simulation_type(client_code=client_code, property_code=property_code),
            eval_args=eval_args,
            pcode=property_code,
            current_target_avg_num_opts=get_current_avg_target_opts(client_code=client_code, property_code=property_code),
            max_num_opts_per_day=total_opts,
            min_date_for_eval=min_date_for_eval,
            processing_data=input_processing_df,
            override_to_full_window_update=should_ovr_full_window_update(client_code=client_code, property_code=property_code),
            include_scheduled_processings=should_include_scheduled_idp(client_code=client_code, property_code=property_code),
            prefer_observed=should_prefer_observed_avg_opts(client_code=client_code, property_code=property_code),
            tolerable_binary_search_error=get_tolerable_binary_search_error(client_code=client_code, property_code=property_code),
            max_num_dyn_opts_per_day=total_dyn_opts
        )
        self.logger.info(f'Revision result for client_code: {client_code} and property_code: {property_code}, new percentile: {new_percentile}, target: {target}, observedNopts: {observed_nopts}, reason: {adjustment_type}, medMaxShiftLRV: {med_max_shift_lrv}, medMaxShiftDeltaLRV: {med_max_shift_delta_lrv}')

        today = date.today().isoformat()
        ucs_service.update_param(client_code=client_code, property_code=property_code,
                                 param_name=UCSConfigParam.CALIBRATION_PERCENTILE.get_full_name(),
                                 new_value=new_percentile)
        ucs_service.update_param(client_code=client_code, property_code=property_code,
                                 param_name=UCSConfigParam.CURR_TARGET_AVG_OPTS.get_full_name(),
                                 new_value=target)
        thresh_func = self.get_latest_quantile_function(client_code=client_code, property_code=property_code)
        revised_potentials = self.get_potentials(calib_percentile=new_percentile, threshold_func=thresh_func)
        self.dynamodb_service.update_potentials(CALIBRATED_POTENTIAL_TABLE, revised_potentials, client_code, property_code)
        self.dynamodb_service.update_last_threshold_revision_date(client_code=client_code, property_code=property_code, caught_up_date=today)

    # Called during full recalibration.
    def get_calibrated_percentile(self, client_code: str, property_code: str, cur_percentile: float) -> float:
        idp_window = dynamodb_service.fetch_idp_window(client_code=client_code, property_code=property_code)
        change_lrv = dynamodb_service.fetch_median_abs_lrv_change(client_code=client_code, property_code=property_code)
        lrv_change_df = calculate_lrv_change(change_lrv, idp_window)
        user_waste_weight = get_user_waste_weight(client_code=client_code, property_code=property_code)
        waste_threshold = get_waste_threshold(client_code=client_code, property_code=property_code)
        regret_threshold = get_regret_threshold(client_code=client_code, property_code=property_code)
        waste_regret_min_data_req = get_waste_regret_min_data(client_code=client_code, property_code=property_code)
        percentile = compute_new_percentile(lrv_change_df, waste_threshold=waste_threshold,
                                            regret_threshold=regret_threshold,
                                            current_calib_percentile=cur_percentile,
                                            user_waste_weight=user_waste_weight,
                                            min_waste_regret_sum=waste_regret_min_data_req)
        self.logger.info(f"Calibrated Percentile: {percentile}")
        return percentile

    def run_calibration_from_local_files(self, local_path_holder: CalibrationLocalFileInput):
        self.logger.info(f"Calibrating {local_path_holder}")
        try:
            return run_calibration(file_reader=pd.read_csv
                                   , delta_occ_solds=local_path_holder.delta_occ_solds
                                   , reference_rate=local_path_holder.reference_rate
                                   , delta_lrv=local_path_holder.delta_lrv
                                   , window_size=local_path_holder.max_data_limit
                                   , min_data_req=local_path_holder.min_data_requirement
                                   , percentile=50
                                   , use_leading_window=False
                                   , fail_on_missing_occ_dates=True
                                   , pressure_floor=0.)[0]
        except Exception as e:
            self.logger.exception(msg=f"Cannot calibrate with {local_path_holder} due to {e}")
            raise e

    def update_perc_and_potentials(self, client_code: str, property_code: str, percentile: float):
        ucs_service.update_param(client_code=client_code, property_code=property_code,
                                 param_name=UCSConfigParam.CALIBRATION_PERCENTILE.get_full_name(),
                                 new_value=percentile)
        thresh_func = self.get_latest_quantile_function(client_code=client_code, property_code=property_code)
        revised_potentials = self.get_potentials(calib_percentile=percentile, threshold_func=thresh_func)
        self.dynamodb_service.update_potentials(CALIBRATED_POTENTIAL_TABLE, revised_potentials, client_code, property_code)
        print(f'Successfully updated {property_code} to {percentile}')


if __name__ == '__main__':
    setup_logger()
    r = CalibrationRequestSource(client_code='Hilton', property_code='AAHHX',
                                 destination_table='dyn-opt-calibrated-potentials',
                                 request_context=CalibrationS3FileInput(
                                     delta_occ_solds='s3://dyn-opt-data/Hilton/AAHHX/calibration/2025-09-26/deltaOcc.csv',
                                     reference_rate='s3://dyn-opt-data/Hilton/AAHHX/calibration/2025-09-26/referencePricePace.csv',
                                     delta_lrv='s3://dyn-opt-data/Hilton/AAHHX/calibration/2025-09-26/deltaLrvPace.csv',
                                     min_data_requirement=21, max_data_limit=21))
    CalibrationService().run_full_calibration_from_s3_save_results(r)

