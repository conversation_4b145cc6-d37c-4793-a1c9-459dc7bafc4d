import logging
from enum import Enum

from requests import HTTPError

from src.common.dto.ucs_param import UCSParam
from src.common.enums.env_var import EnvironmentVariable
from src.common.env import get_value
from src.common.exceptions.ucs_exception import UCSException
from src.common.http_authorizers.ucs_auth import UCSAuth
from src.common.http_authorizers.uis_auth import UISAuth
from src.common.http_service import HttpService
from src.common.pydantic_util import from_dict_array


class UCSService:
    GET_CONFIG_PARAM_PATH = 'v1/config-params'

    def __init__(self, ucs_http_service: HttpService):
        self.ucs_http_service = ucs_http_service
        self.logger = logging.getLogger(__name__)

    def update_param(self, *, client_code: str, property_code, param_name, new_value):
        if isinstance(param_name, UCSConfigParam):
            param_name = param_name.get_full_name()
        body = {
            'clientCode': client_code.upper(),
            'propertyCode': property_code,
            'paramName': param_name,
            'propertyLevelValue': str(new_value)
        }
        headers = {
            'Content-Type': 'application/json'
        }
        self.ucs_http_service.post(self.GET_CONFIG_PARAM_PATH, body=body, headers=headers)
        return f"Successfully updated param {param_name} to {new_value}"

    def get_config_param_value(self, param, client_code, property_code):
        request = {
            'paramName': param,
            'clientCode': client_code,
            'propertyCode': property_code,
            'group': CONFIG_GROUP,
            'category': CONFIG_CATEGORY
        }
        response = self.ucs_http_service.get_raw_response(self.GET_CONFIG_PARAM_PATH, params=request)
        if response.status_code == 204 or response.reason == 'No Content':
            raise UCSException(f"Param Not found on ucs: {param}")
        ucs_params: list[UCSParam] = from_dict_array(list[UCSParam], response.json())
        if len(ucs_params) > 1:
            raise UCSException(f"UnExpected response size from ucs for: {param}")

        return ucs_params[0].get_typed_value()

    def get_or_default(self, param: str, *, default, client_code, property_code):
        try:
            return self.get_config_param_value(param, client_code, property_code)
        except HTTPError as e:
            self.logger.warning(f"Using default value: \n {str(e)}")
            return default
        except UCSException as u:
            self.logger.warning(f"Using default value: \n {u.message}")
            return default


ucs_service = UCSService(HttpService(get_value(EnvironmentVariable.UCS_BASE_URL, ''),
                                     authorizer=UCSAuth(HttpService(get_value(EnvironmentVariable.UIS_AUTH_URL, ''),
                                                                    authorizer=UISAuth()))))

CONFIG_GROUP = 'g3'
CONFIG_CATEGORY = 'dynamicOptimization'

logger = logging.getLogger(__name__)


class UCSConfigParam(Enum):
    FAIL_ON_MISSING_OCCUPANCY_DATES = 'failOnMissingOccupancyDates'
    CALIBRATION_USE_LEADING_WINDOW = 'calibrationUseLeadingWindow'
    CALIBRATION_ROLLING_WINDOW = 'calibrationRollingWindow'
    CALIBRATION_PERCENTILE = 'calibrationPercentile'
    PRESSURE_FLOOR = 'pressureFloor'
    MIN_HEURISTIC_OCC_PERC_CHANGE_THRESHOLD = 'minHeuristicOccPercChangeThreshold'
    WASTE_THRESHOLD = 'wasteThreshold'
    REGRET_THRESHOLD = 'regretThreshold'
    USER_WASTE_WEIGHT = 'userWasteWeight'
    WASTE_REGRET_MIN_DATA = 'wasteRegretMinData'
    PERSIST_EVAL_OP_AT_RC_LVL = 'persistEvaluationOutputAtRcLevel'
    MAX_IDP_RECOM_COUNT = 'maxIdpRecommendationCount'
    TOTAL_SCHEDULED_IDP_COUNT = 'totalScheduledIdpCount'
    MIN_SHIFT_DELTA_LRV_THRESHOLD = 'minShiftDeltaLRVThresh'
    MAX_SHIFT_DELTA_LRV_THRESHOLD = 'maxShiftDeltaLRVThresh'
    MIN_SHIFT_LRV_THRESHOLD = 'minShiftLRVThresh'
    MAX_SHIFT_LRV_THRESHOLD = 'maxShiftLRVThresh'
    MAX_ALLOWABLE_PERCENTILE = 'maxAllowablePercentile'
    MIN_ALLOWABLE_PERCENTILE = 'minAllowablePercentile'
    SIMULATION_TYPE = 'simulationType'
    CURR_TARGET_AVG_OPTS = 'currentTargetAvgNumOpts'
    OVR_FULL_WINDOW_UPDATE = 'overrideToFullWindowUpdate'
    INC_SCHEDULED_IDP = 'includeScheduledProcessings'
    PREFER_OBSERVED_AVG_OPTS = 'preferObservedNAvgOpts'
    TOLERABLE_BINARY_SEARCH_ERROR = 'tolerableBinarySearchError'
    ENABLE_UPLOAD_TO_S3 = 'enableUploadToS3'
    ENABLE_READ_FROM_S3 = 'enableReadFromS3'


    def get_full_name(self):
        return f'{CONFIG_GROUP}.{CONFIG_CATEGORY}.{self.value}'

    def fetch_value(self, client_code: str, property_code):
        value = ucs_service.get_config_param_value(self.get_full_name(), client_code.upper(), property_code)
        logger.info(f"{self.value}:{value}")
        return value


def to_bool(value):
    return str(value).lower() in ['true', '1']


def should_fail_on_missing_occupancy_dates(client_code, property_code) -> bool:
    return to_bool(UCSConfigParam.FAIL_ON_MISSING_OCCUPANCY_DATES.fetch_value(client_code, property_code))


def get_calibration_rolling_window(client_code, property_code) -> int:
    return int(UCSConfigParam.CALIBRATION_ROLLING_WINDOW.fetch_value(client_code, property_code))


def get_calibration_percentile(client_code, property_code) -> int:
    return int(UCSConfigParam.CALIBRATION_PERCENTILE.fetch_value(client_code, property_code))


def should_use_leading_window(client_code, property_code) -> bool:
    return to_bool(UCSConfigParam.CALIBRATION_USE_LEADING_WINDOW.fetch_value(client_code, property_code))


def get_pressure_floor(client_code, property_code) -> float:
    return float(UCSConfigParam.PRESSURE_FLOOR.fetch_value(client_code, property_code))


def get_occ_change_threshold(client_code, property_code) -> float:
    return float(UCSConfigParam.MIN_HEURISTIC_OCC_PERC_CHANGE_THRESHOLD.fetch_value(client_code, property_code))



def get_waste_threshold(client_code, property_code) -> int:
    return int(UCSConfigParam.WASTE_THRESHOLD.fetch_value(client_code, property_code))

def get_max_idp_recommendation_cnt(client_code, property_code) -> int:
    return int(UCSConfigParam.MAX_IDP_RECOM_COUNT.fetch_value(client_code, property_code))


def get_regret_threshold(client_code, property_code) -> int:
    return int(UCSConfigParam.REGRET_THRESHOLD.fetch_value(client_code, property_code))


def get_user_waste_weight(client_code, property_code) -> float:
    return float(UCSConfigParam.USER_WASTE_WEIGHT.fetch_value(client_code, property_code))


def get_waste_regret_min_data(client_code, property_code) -> int:
    return int(UCSConfigParam.WASTE_REGRET_MIN_DATA.fetch_value(client_code, property_code))

def get_persist_eval_op_at_rc_lvl(client_code, property_code) -> bool:
    return to_bool(UCSConfigParam.PERSIST_EVAL_OP_AT_RC_LVL.fetch_value(client_code, property_code))

def get_total_scheduled_idp_count(client_code, property_code) -> int:
    return int(UCSConfigParam.TOTAL_SCHEDULED_IDP_COUNT.fetch_value(client_code, property_code))

def get_min_shift_delta_lrv_threshold(client_code, property_code) -> float:
    return float(UCSConfigParam.MIN_SHIFT_DELTA_LRV_THRESHOLD.fetch_value(client_code, property_code))

def get_max_shift_delta_lrv_threshold(client_code, property_code) -> float:
    return float(UCSConfigParam.MAX_SHIFT_DELTA_LRV_THRESHOLD.fetch_value(client_code, property_code))

def get_min_shift_lrv_threshold(client_code, property_code) -> float:
    return float(UCSConfigParam.MIN_SHIFT_LRV_THRESHOLD.fetch_value(client_code, property_code))

def get_max_shift_lrv_threshold(client_code, property_code) -> float:
    return float(UCSConfigParam.MAX_SHIFT_LRV_THRESHOLD.fetch_value(client_code, property_code))

def get_max_allowable_percentile(client_code, property_code) -> int:
    return int(UCSConfigParam.MAX_ALLOWABLE_PERCENTILE.fetch_value(client_code, property_code))

def get_min_allowable_percentile(client_code, property_code) -> int:
    return int(UCSConfigParam.MIN_ALLOWABLE_PERCENTILE.fetch_value(client_code, property_code))

def get_simulation_type(client_code, property_code) -> str:
    return UCSConfigParam.SIMULATION_TYPE.fetch_value(client_code, property_code)

def get_current_avg_target_opts(client_code, property_code) -> float:
    return float(UCSConfigParam.CURR_TARGET_AVG_OPTS.fetch_value(client_code, property_code))

def should_ovr_full_window_update(client_code, property_code) -> bool:
    return to_bool(UCSConfigParam.OVR_FULL_WINDOW_UPDATE.fetch_value(client_code, property_code))

def should_include_scheduled_idp(client_code, property_code) -> bool:
    return to_bool(UCSConfigParam.INC_SCHEDULED_IDP.fetch_value(client_code, property_code))

def should_prefer_observed_avg_opts(client_code, property_code) -> bool:
    return to_bool(UCSConfigParam.PREFER_OBSERVED_AVG_OPTS.fetch_value(client_code, property_code))

def get_tolerable_binary_search_error(client_code, property_code) -> float:
    return float(UCSConfigParam.TOLERABLE_BINARY_SEARCH_ERROR.fetch_value(client_code, property_code))

def should_upload_to_s3(client_code, property_code) -> bool:
    return to_bool(UCSConfigParam.ENABLE_UPLOAD_TO_S3.fetch_value(client_code, property_code))

def should_read_from_s3(client_code, property_code) -> bool:
    return to_bool(UCSConfigParam.ENABLE_READ_FROM_S3.fetch_value(client_code, property_code))