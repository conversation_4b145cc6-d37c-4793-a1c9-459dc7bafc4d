import warnings
from dataclasses import dataclass
from datetime import date
from typing import <PERSON>ple, Dict

import pandas as pd

from src.algorithm.Potentials.Potentials import PotentialMethods
from src.algorithm.Utilities.GenericUtilities.GenericDeltaLRVHandler import GenericDeltaLRVHandler
from src.algorithm.Utilities.GenericUtilities.GenericDeltaOccHandler import GenericDeltaOccHandler
from src.algorithm.Utilities.GenericUtilities.GenericRefRateHandler import Generic<PERSON>efRateHandler
from src.algorithm.dynamic_optimization_package.CommonDynamicOptElements import CommonDOElements


@dataclass
class EvaluateDynamicOptimization(CommonDOElements):
    deltaOccHandlerDict: Dict[int | str, Dict[date, <PERSON>ric<PERSON>eltaOccHandler]]
    refRateHandlerDict: Dict[int | str, Dict[date, GenericRefRateHandler]]
    deltaLRVHandlerDict: Dict[int | str, Dict[date, <PERSON>ricDeltaLRVHandler]]
    calibrated: Dict[int | str, float]  # these contain the thresholdPotential by RC
    potentialMode: str
    useLeadingWindow: bool = False
    fail_on_missing_occ_dates: bool = False
    windowSize: int = 21
    pressure_floor: float = 0.0
    minHeuristicOccPercChangeThreshold:float=0.

    def __post_init__(self):
        self.debugMode = False
        self.potentialMode = self.potentialMode.lower()


    def __toggleDebug__(self):
        if not self.debugMode:
            self.debugMode = True
        else:
            self.debugMode = False

    def evaluate(self) -> Dict[int | str, Tuple[bool, date | None]] | Tuple[Dict[int | str, Tuple[bool, date | None]],pd.DataFrame]:
        rcacCol = None
        rclist = self.get_intersection(
            [self.refRateHandlerDict.keys(), self.deltaLRVHandlerDict.keys(), self.deltaOccHandlerDict.keys()])
        results = {}
        if self.debugMode:
            assemblagelist = []
        for rc in rclist:
            deltaOccHandlerByCapdate = self.deltaOccHandlerDict[rc]
            refRateByCapDate = self.refRateHandlerDict[rc]
            deltaLRVHandlerByCapDate = self.deltaLRVHandlerDict[rc]
            capDateList = self.get_intersection(
                [deltaOccHandlerByCapdate.keys(), refRateByCapDate.keys(), deltaLRVHandlerByCapDate.keys()])
            if not len(capDateList) == 1:
                raise RuntimeError(
                    "Must have only a single capturedate for evaluation of Dynamic Optimization Triggers")
            for capday in capDateList:
                deltaocchandler = deltaOccHandlerByCapdate[capday]
                deltalrvhandler = deltaLRVHandlerByCapDate[capday]
                refRateHandler = refRateByCapDate[capday]
                rcacCol=deltaocchandler.rcacCol
                assemblage, isSufficientData = self.build_individual_stuff(deltaocchandler=deltaocchandler,
                                                                           deltalrvhandler=deltalrvhandler,
                                                                           refRateHandler=refRateHandler,
                                                                           use_leading_window=self.useLeadingWindow,
                                                                           fail_on_missing_occupancy_dates=self.fail_on_missing_occ_dates,windowSize=self.windowSize)
                # results[rc] = PotentialMethods.evaluatePotential(potentialMode=self.potentialMode,assemblage=assemblage,dateCol=deltaocchandler.dateCol,thresholdPotential=self.calibrated[rc]['calibPotential'].to_list()[0], hasSufficientData=isSufficientData)
                results[rc] = PotentialMethods.evaluatePotential(
                    potentialMode=self.potentialMode,
                    assemblage=assemblage,
                    dateCol=deltaocchandler.dateCol,
                    thresholdPotential=self.calibrated.get(rc, CommonDOElements.__insufficientDataThresholdPotential__),
                    hasSufficientData=isSufficientData,
                    debugMode=self.debugMode,
                    pressure_floor=self.pressure_floor,
                    occChange_threshold=self.minHeuristicOccPercChangeThreshold,
                    occChangeCol='__rollingMaxPercChangeOcc__'
                )

                # Add the assemblage to the list if debug mode is enabled
                if self.debugMode:
                    assemblagelist.append(assemblage)

        # Return results and assemblagelist if debug mode is enabled
        if self.debugMode:
            if assemblagelist:
                return results, pd.concat(assemblagelist, ignore_index=True)
            else:
                return results, pd.DataFrame(
                    columns=[rcacCol, 'staydate', 'lt', 'deltaOcc', 'capturedate', 'deltalrv', 'refRate', 'ai', 'ai2',
                             'pressure', 'potential','__rollingMaxPercChangeOcc__'])
        return results
