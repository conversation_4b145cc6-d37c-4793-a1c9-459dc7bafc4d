import numpy as np
import pandas as pd
from src.algorithm.Utilities.GenericUtilities.FillMissingDates import FillMissingDates

service = FillMissingDates()


def __calculate_daily_median_change(idp_window_for_caught_up_date: pd.DataFrame):
    lrv_changes = []
    for i in idp_window_for_caught_up_date.index:
        median_lrv_change = idp_window_for_caught_up_date.loc[i]['medianLrvChange']
        window = idp_window_for_caught_up_date.loc[i]['window']
        median_change_window_times = [median_lrv_change] * int(window)
        lrv_changes = lrv_changes + median_change_window_times
    return {'caughtUpDate': idp_window_for_caught_up_date.name, 'medianLrvChange': np.median(lrv_changes),
            'noOfOpt': len(idp_window_for_caught_up_date)}


def __get_lrv_change(change_lrv: pd.DataFrame, idp_window_record: pd.Series):
    window: pd.Timedelta = idp_window_record['maxOccupancyDate'] - idp_window_record['caughtUpDate']
    caught_up_date = idp_window_record['caughtUpDate']
    evaluation_time = idp_window_record['evaluationTime']
    possible_opt = change_lrv.query('caughtUpDate == @caught_up_date and optimizationTime >= @evaluation_time')
    if possible_opt.empty:
        return [np.nan, np.nan]
    p = possible_opt.iloc[0]
    return [p['medianLrvChange'], window.days]


def calculate_lrv_change(change_lrv: pd.DataFrame, idp_window: pd.DataFrame):
    idp_window['maxOccupancyDate'] = pd.to_datetime(idp_window['maxOccupancyDate'])
    idp_window['caughtUpDate'] = pd.to_datetime(idp_window['caughtUpDate'])
    change_lrv['caughtUpDate'] = pd.to_datetime(change_lrv['caughtUpDate'])
    change_lrv['optimizationTime'] = pd.to_datetime(change_lrv['optimizationTime'])
    change_lrv = change_lrv.sort_values(['caughtUpDate', 'optimizationTime'])
    idp_window[['medianLrvChange', 'window']] = idp_window.apply(lambda x: __get_lrv_change(change_lrv, x), axis=1,
                                                                 result_type='expand')
    idp_window = idp_window.dropna()
    if idp_window.empty:
        return pd.DataFrame()
    lrv_change = pd.DataFrame(idp_window.groupby('caughtUpDate').apply(__calculate_daily_median_change).tolist())
    return service.fill_missing_dates(df=lrv_change, date_column='caughtUpDate')
