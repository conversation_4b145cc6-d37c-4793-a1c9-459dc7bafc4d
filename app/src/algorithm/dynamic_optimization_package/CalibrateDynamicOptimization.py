import warnings
from dataclasses import dataclass
from datetime import date
from functools import partial
from typing import <PERSON><PERSON>, Dict, Callable

import numpy as np
import pandas as pd
from pandas.core.groupby import GroupBy

from src.algorithm.Potentials.Potentials import PotentialMethods
from src.algorithm.Utilities.GenericUtilities.GenericDeltaLRVHandler import GenericDeltaLRVHandler
from src.algorithm.Utilities.GenericUtilities.GenericDeltaOccHandler import GenericDel<PERSON><PERSON><PERSON><PERSON>and<PERSON>
from src.algorithm.Utilities.GenericUtilities.GenericRefRateHandler import GenericRefRateHandler
from src.algorithm.dynamic_optimization_package.CommonDynamicOptElements import CommonDOElements


@dataclass
class CalibrationDynamicOptimization(CommonDOElements):
    deltaOccHandlerDict: Dict[int | str, Dict[date, GenericDeltaOccHandler]]
    refRateHandlerDict: Dict[int | str, Dict[date, GenericRefRateHandler]]
    deltaLRVHandlerDict: Dict[int | str, Dict[date, <PERSON>ricDeltaLRVHandler]]
    potentialMode: str
    useLeadingWindow: bool = False
    failOnMissingOccupancyDates: bool = False
    windowSize: int = 21
    calibQuantile: float = 50
    pressure_floor: float = 0.0

    def __post_init__(self):
        self.debugMode = False
        if self.calibQuantile > 100 or self.calibQuantile < 0.:
            raise RuntimeError(
                f"calibQuantile={self.calibQuantile} is not allowed. Please select a value between [0.,1.]")
        self.potentialMode = self.potentialMode.lower()
        if self.windowSize <= 0:
            raise RuntimeError(f"window Size {self.windowSize} must be an integer greater than 0")

    def __toggleDebug__(self):
        if not self.debugMode:
            self.debugMode = True
        else:
            self.debugMode = False

    def calibrateWithL(self, startRange: int = 1, endRange: int = 60):
        warnings.warn('Calibrate with L is not currently ready for use. Do not use. Present only for testing')
        currentMaxL = self.windowSize
        isDebugMode = self.debugMode
        if not self.debugMode:
            self.__toggleDebug__()
        sumlist = []
        calibDict = {}
        ftpDict = {}
        if isDebugMode:
            assembDict = {}
        for l in range(startRange, endRange + 1):
            self.windowSize = l
            calibDict[l], assemblage, ftpDict[l] = self.calibrate()
            assemblage['potential'] = np.log(assemblage['pressure'])
            assemblage['windowSize'] = l
            if isDebugMode:
                assembDict[l] = assemblage
            summary = assemblage.query(f'pressure>{self.pressure_floor}').groupby(self.rcacCol, as_index=False).agg(
                meanPot=('potential', 'mean')
                , stdPot=('potential', 'std'))
            summary['l'] = l
            summary['coeffVar'] = summary['stdPot'].fillna(0.) / summary['meanPot'].abs()
            sumlist.append(summary)
        summary = pd.concat(sumlist, ignore_index=True).sort_values([self.rcacCol, 'coeffVar'], ignore_index=True,
                                                                    ascending=[True, False])
        selectedL = summary.groupby([self.rcacCol], as_index=False).nth[0]
        asdf = {}
        for row in selectedL.to_dict(orient='records'):
            rcac = row[self.rcacCol]
            lval = row['l']
            asdf[rcac] = {'calibPotential': calibDict[lval][rcac], 'calibWindowSize': lval}
        calibByRC = {row[self.rcacCol]: {'calibPotential': calibDict[row['windowSize']][row[self.rcacCol]],
                                         'calibWindowSize': row['windowSize']} for row in
                     selectedL.to_dict(orient='records')}
        finalTotalPressure = pd.concat(
            [ftpDict[row['windowSize']].query(f'{self.rcacCol}=={row[self.rcacCol]}') for row in
             selectedL.to_dict(orient='records')], ignore_index=True)
        if isDebugMode:
            assemblage = pd.concat(
                [assembDict[row['windowSize']].query(f'{self.rcacCol}=={row[self.rcacCol]}') for row in
                 selectedL.to_dict(orient='records')], ignore_index=True)
            return calibByRC, assemblage, finalTotalPressure
        self.__toggleDebug__()  # setting it back to its original value
        return calibByRC, finalTotalPressure

    def calibrate(self) -> pd.DataFrame | Tuple[pd.DataFrame, pd.DataFrame]:
        rcacCol = '__emptyRCCol__'
        rclist = self.get_intersection(
            [self.refRateHandlerDict.keys(), self.deltaLRVHandlerDict.keys(), self.deltaOccHandlerDict.keys()])
        calibByRC = {}
        final_total_pressures = []
        if self.debugMode:
            assemblagelist = []
        for rc in rclist:
            deltaOccHandlerByCapdate = self.deltaOccHandlerDict[rc]
            refRateByCapDate = self.refRateHandlerDict[rc]
            deltaLRVHandlerByCapDate = self.deltaLRVHandlerDict[rc]
            capDateList = self.get_intersection(
                [deltaOccHandlerByCapdate.keys(), refRateByCapDate.keys(), deltaLRVHandlerByCapDate.keys()])
            tplist = []
            aslist = []
            for capday in capDateList:
                # perform a data sufficiency check
                deltaocchandler = deltaOccHandlerByCapdate[capday]
                deltalrvhandler = deltaLRVHandlerByCapDate[capday]
                refRateHandler = refRateByCapDate[capday]
                rcacCol = deltaocchandler.rcacCol
                assemblage, isSufficientData = self.build_individual_stuff(deltaocchandler=deltaocchandler,
                                                                           deltalrvhandler=deltalrvhandler,
                                                                           refRateHandler=refRateHandler,
                                                                           fail_on_missing_occupancy_dates=self.failOnMissingOccupancyDates,
                                                                           windowSize=self.windowSize)

                if isSufficientData:
                    totalPressure = PotentialMethods.computePotential(potentialMode=self.potentialMode,
                                                                      assemblage=assemblage,
                                                                      dateCol=deltaocchandler.dateCol,
                                                                      pressure_floor=self.pressure_floor)
                    if not totalPressure.empty:
                        tplist.append(totalPressure)
                    if self.debugMode and not assemblage.empty:
                        aslist.append(assemblage)
            if not tplist or not aslist:
                # if we don't have a value then we pass a potential threshold that can never be achieved
                calibByRC[rc] = CommonDOElements.__insufficientDataThresholdPotential__
            else:
                finalTotalPressure = pd.concat(tplist, ignore_index=True)
                assemblagefinal = pd.concat(aslist, ignore_index=True)
                finalTotalPressure[rcacCol] = rc
                if self.debugMode:
                    assemblagelist.append(assemblagefinal)
                calibratedValues = finalTotalPressure.groupby(by=rcacCol, as_index=False).agg(
                    calibPotential=('potential', lambda x: np.percentile(x, self.calibQuantile)))[
                    'calibPotential'].to_list()[0]
                calibByRC[rc] = calibratedValues
                final_total_pressures.append(finalTotalPressure)
        final_total_pressures = pd.concat(final_total_pressures, ignore_index=True) if len(
            final_total_pressures) > 0 else pd.DataFrame(columns=[rcacCol])
        quantile_fun = self.quantile_function(final_total_pressures, rcacCol, calibByRC)

        if self.debugMode:
            if assemblagelist:
                return calibByRC, pd.concat(assemblagelist, ignore_index=True), quantile_fun
            else:
                return calibByRC, pd.DataFrame(
                    columns=[rcacCol, 'staydate', 'lt', 'deltaOcc', 'capturedate', 'deltalrv', 'refRate', 'ai', 'ai2',
                             'pressure', 'potential', '__rollingMaxPercChangeOcc__']), quantile_fun
        return calibByRC, quantile_fun

    def quantile_function(self, finalTotalPressures: pd.DataFrame, rc_col, calib_potential: dict[int, float]):
        quantile_fun_by_rc: dict[str, dict[int, Callable]] = finalTotalPressures.groupby(by=rc_col).apply(
            lambda x: self.get_quantile_function_for_rc(x, calib_potential)).to_dict()
        quantile_fun_by_rc = quantile_fun_by_rc[rc_col] if rc_col in quantile_fun_by_rc.keys() else quantile_fun_by_rc
        return {rc: quantile_fun_by_rc.get(rc, self.get_default_quantile_function) for rc in calib_potential.keys()}

    @staticmethod
    def get_default_quantile_function(x):
        return CommonDOElements.__insufficientDataThresholdPotential__

    @staticmethod
    def get_quantile_function_for_rc(finalTotalPressure: GroupBy, calib_potential):
        # generate a distribution function
        rc = finalTotalPressure.name
        if calib_potential[rc] == CommonDOElements.__insufficientDataThresholdPotential__:
            return CalibrationDynamicOptimization.get_default_quantile_function
        finalTotalPressure['__dummyVar__'] = 1
        quantilesResults = []
        testQuantiles = [x for x in range(101)]
        for testQuantile in testQuantiles:
            testCalib = finalTotalPressure.groupby(by='__dummyVar__', as_index=False).agg(
                calibPotential=('potential', lambda x: np.percentile(x, testQuantile)))['calibPotential'].to_list()[0]
            quantilesResults.append(testCalib)
        interpFun = partial(np.interp, xp=testQuantiles, fp=quantilesResults)
        return interpFun

# if __name__ == '__main__':
#     fp = s3_service.fetch_file("s3://dyn-opt-data/Hilton/DXBCD/calibration/2025-01-13/final_total_pressure.csv")
#     fun = quantile_function(fp, 'accomClassId', {8: 1., 6: 1., 7: 1., 9: 1.})
#     import pickle
#
#     pickle.dumps(fun)
