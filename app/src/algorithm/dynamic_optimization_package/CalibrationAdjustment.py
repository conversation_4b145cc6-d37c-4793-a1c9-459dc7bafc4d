from typing import Callable

import numpy as np
import pandas as pd

"""
This is a function which computes both a new calibration percentile (one of the configurable parameters) and estimates
a new re-optimization threshold without the need for a full calibration. We do this based on the waste and regret
created by allowing the algorithm to run. Here we define:
    - waste: number of days for which we do not run enough optimizations
    - regret: number of days for which we run too many optimizaitons
The algorithm functions by performing 2 binary searches, one to reduce regret and one to reduce waste.
The two results are then counterbalanced against the current calibration value, user preference of importance,
and the impact these have on the LRV. E.g. if we calibrate too often and have only small changes to the LRV, then
we probably don't want to lend as much weight to the LRV

To accomplish this we define a new quantity:
    - Accumulated LRV: The total absolute change in LRV created by each optimization.
We can compute an LRV weight by
    1) computing the total accumulated LRV on days with either regret or waste labeling it as badAccumLRV
    2) computing the total accumulated LRV with regret and waste individually
    3) Compute a wasteLRVWeight by wasteAccumLRV/badAccumLRV

:param wasteRegretDf: Dataset which has a count of optimizations performed for that capturedate and the accumulated absolute LRV change
:param wasteThreshold: the number of days considered the threshold for too few optimizations
:param regretThreshold: the number of days considered to be the threshold for too many optimizations
:param threshCalcFun: A function created by the calibration procedure to allow us to compute a new threshold using a new calib percentile
:param currentCalibPercentile: self explanatory
:param userWasteWeight: We objectively want few optimizations from a cost perspective so this parameter allows us to further downweight the impact of waste
:param minDataReq: minumum number of days with either waste or regret before we bother
:param accumulatedLRVCol: Explained above. Its the LRV impact of a calibration
:param optCountCol: Column for the dataset which has the count of optimizations
:param maxCalibBound: max calib quantile allowable
:param minCalibBound: min Calib quantile allowable
:param isFullCalibProcess:Dictates whether or not we expect a distribution function given that a full recalibration won't necessarily have one
:return:
"""


def get_new_threshold_from_fun(percentile_guess, thresh_calc_fun: Callable[[float], float]):
    return thresh_calc_fun(percentile_guess)


def guess_percentile(wasteRegretDf: pd.DataFrame, wasteThreshold: int, regretThreshold: int,
                     currentCalibPercentile: float, userWasteWeight: float = 0.2, minDataReq=5,
                     accumulatedLRVCol: str = 'accumLRV', optCountCol: str = 'nOpt', maxCalibBound: float = 100,
                     minCalibBound: float = 0,minLRVDifferenceThreshold:float=1):
    if wasteRegretDf.empty:
        raise Exception("Not enough data to make a percentile guess")
    # label days with waste and regret
    # if we have 100% Waste we don't run enough. We need to raise the fraction half way between current and max
    # If we have 100% regret we need to lower the value to half way between current and min
    wasteRegretDf['hasWaste'] = wasteRegretDf[optCountCol] < wasteThreshold
    wasteRegretDf['hasRegret'] = wasteRegretDf[optCountCol] > regretThreshold

    # now get waste and regret count.
    wasteCount = wasteRegretDf['hasWaste'].sum()
    # regret Count
    regretCount = wasteRegretDf['hasRegret'].sum()

    # if the waste and regret is negligible, then don't bother changing anything
    if wasteCount + regretCount <= minDataReq:
        raise Exception("Not enough data to make a percentile guess")

    # total number of days
    nEntries = len(wasteRegretDf)

    # frequencyOfDaysWithWaste
    wasteWeight = wasteCount / nEntries

    # frequency of days with regret
    regretWeight = regretCount / nEntries

    # guess a new calibration percentile as if we had 100% waste and wanted to do a binary search to find a better value
    binaryWasteGuess = currentCalibPercentile - (currentCalibPercentile - minCalibBound) / 2

    # # guess a new calibration percentile as if we had 100% regret and wanted to do a binary search to find a better value
    binaryRegretGuess = currentCalibPercentile + (maxCalibBound - currentCalibPercentile) / 2

    # wasteGuess: here we scale by size of the waste. If its small we lean towards the current percentile if the waste is large
    # we prefer to make larger changes in the calibration percentile
    wasteGuess = binaryWasteGuess * wasteWeight + (1 - wasteWeight) * currentCalibPercentile

    #  similarly for regret
    regretGuess = binaryRegretGuess * regretWeight + (1 - regretWeight) * currentCalibPercentile

    # we also need to incoprorate the accumulated change in LRV. If the waste/regret doesn't result in much change
    # in LRV, then the motivation to change it is low. This is especially true of days with regret

    # accumulated LRV values
    medianLRVChangeWaste = (wasteRegretDf['hasWaste'] * wasteRegretDf[accumulatedLRVCol]).abs().median()
    medianLRVChangeRegret = (wasteRegretDf['hasRegret'] * wasteRegretDf[accumulatedLRVCol]).abs().median()
    totalMedianLRVChange = medianLRVChangeWaste + medianLRVChangeRegret

    # acting on extremely small chagnes helps no one
    if abs(medianLRVChangeWaste-medianLRVChangeRegret)<minLRVDifferenceThreshold:
        lrvWasteWeight=0.5
    else:
        lrvWasteWeight = medianLRVChangeWaste / totalMedianLRVChange
    # Now calculate the percentage of the waste/regret lrv that goes to either waste or regret.
    lrvRegretWeight = 1 - lrvWasteWeight

    # the final weighting is the combination of all weights which will be normalized
    finalWasteWeight = lrvWasteWeight * wasteWeight * userWasteWeight
    finalRegretWeight = lrvRegretWeight * regretWeight * (1 - userWasteWeight)

    # normalizationFactor
    normalizationFactor = finalRegretWeight + finalWasteWeight
    # final guess
    new_percentile = (1 / normalizationFactor) * (finalWasteWeight * wasteGuess + finalRegretWeight * regretGuess)
    return currentCalibPercentile if np.isnan(new_percentile) else new_percentile
