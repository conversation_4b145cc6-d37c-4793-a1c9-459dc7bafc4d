from dataclasses import dataclass
import pandas as pd
from src.algorithm.dynamic_optimization_package.RevisionPackage.ColumnsContainer import RevisionColumnsContainer

@dataclass(slots=True)
class DayDO:
    accom_class_id: float | str
    occupancy_date:pd.Timestamp
    evaluation_time:pd.Timestamp
    lrv:float
    delta_lrv:float
    delta_solds:float
    incremental_solds:float
    ceiling:float
    price:float
    available_capacity:float

    @property
    def isScheduledProcess(self)->bool:
        return self._isScheduledProcessing

    def return_as_row(self):
        return [self.accom_class_id, self.occupancy_date, self.evaluation_time, self.lrv, self.delta_lrv, self.delta_solds,
                self.incremental_solds, self.ceiling, self.price, self.available_capacity]

    @staticmethod
    def get_row_col_order(rc:RevisionColumnsContainer):
        return [rc.accomClassCol,rc.dayCol,rc.evalTimeCol,rc.lrvCol,rc.deltaLRVCol,rc.deltaSoldsCol,rc.incSoldsCol,rc.ceilCol,
                rc.priceCol,rc.availCapCol]

    @classmethod
    def from_dict(cls, data_dict: dict, rc: RevisionColumnsContainer):
        return DayDO(
            accom_class_id=data_dict[rc.accomClassCol]
            , occupancy_date=data_dict[rc.dayCol]
            , evaluation_time=data_dict[rc.evalTimeCol]
            , lrv=data_dict[rc.lrvCol]
            , delta_lrv=data_dict[rc.deltaLRVCol]
            , delta_solds=data_dict[rc.deltaSoldsCol]
            , incremental_solds=data_dict[rc.incSoldsCol]
            , ceiling=data_dict[rc.ceilCol]
            , price=data_dict[rc.priceCol]
            , available_capacity=data_dict[rc.availCapCol]
        )
