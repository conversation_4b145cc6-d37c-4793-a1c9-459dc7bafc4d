import datetime
from dataclasses import dataclass

import pandas as pd
import numpy as np
from src.algorithm.dynamic_optimization_package.RevisionPackage.DayDO import DayDO
from src.algorithm.dynamic_optimization_package.RevisionPackage.ColumnsContainer import RevisionColumnsContainer
from src.algorithm.dynamic_optimization_package.RevisionPackage.ProcessDescriptionContainer import ProcessInfoDO


"""
This class is used to store all of the datasets so that I don't have to compute it hundreds of times
"""

@dataclass
class EvalChangeDO:
    data:dict[pd.Timestamp,dict[float|str|int,dict[pd.Timestamp,DayDO]]] # evalTime,accomClass,stayDate,dayobj
    scheduled_events_causing_resets:dict[pd.Timestamp,ProcessInfoDO]
    rc:RevisionColumnsContainer
    scheduled_process_times: list[pd.Timestamp] | None=None
    _DEFAULT_START_DATE = pd.to_datetime('1969-08-15')
    _DEFAULT_END_DATE = pd.to_datetime('2125-12-31')


    def __post_init__(self):
        if self.scheduled_process_times is None:
            self.scheduled_process_times = [pd.Timestamp('1969-08-15')]
    @classmethod
    def from_dataframe(cls, assemblage:pd.DataFrame, rc:RevisionColumnsContainer, scheduled_process_times: list[pd.Timestamp] | None=None):
        data_set = {}
        scheduled_events_causing_resets =  {}
        for evalTime, evalDF in assemblage.groupby(rc.evalTimeCol):
            eval_set = {}
            prev_type_of_process = ProcessInfoDO.generate_process_info(procType=evalDF['prevEvent'].unique().tolist()[0],relevantProcTimeForScheduled=evalDF['prevEventTime'].unique()[0])
            if prev_type_of_process.is_scheduled:
                scheduled_events_causing_resets[evalTime] = prev_type_of_process

            for accom_class, accom_df in evalDF.groupby(rc.accomClassCol):
                accom_class_set = {}
                for row in accom_df.to_dict(orient='records'):
                    accom_class_set[row[rc.dayCol]]= DayDO.from_dict(data_dict=row, rc=rc)
                eval_set[accom_class] = accom_class_set
            data_set[evalTime] = eval_set
        return EvalChangeDO(data=data_set, rc=rc, scheduled_events_causing_resets=scheduled_events_causing_resets, scheduled_process_times=scheduled_process_times)

    def get_sorted_evaluation_time(self):
        return sorted(list(self.data.keys()))

    def get_incremental_state(self, eval_time:pd.Timestamp)->dict[float | str | int,dict[pd.Timestamp,DayDO]]:
        return self.data[eval_time]

    def get_stay_range_for_eval_date(self, eval_time:pd.Timestamp):
        # this assumes that all relevant days are in the assemblage file
        eval_dict = self.data[eval_time]
        dates = []
        for timeDict in eval_dict.values():
            dates = [*dates,*list(timeDict.keys())]
        min_date  = max(np.min(dates), pd.to_datetime(eval_time.date()))
        max_date = np.max(dates)
        return min_date, max_date

    def get_initial_simulation_state(self):
        earliest = self.get_sorted_evaluation_time()[0]
        ed = self.data[earliest]
        # expects a dataset with first index of accomClass second of stayDate
        return ed

    def get_scheduled_processing_end_date(self, eval_time:pd.Timestamp):
        try:
            return self.scheduled_events_causing_resets[eval_time].opt_window_end_date
        except:
            return self._DEFAULT_END_DATE

    def get_scheduled_processing_actual_processing_eval_time(self, current_eval_time):
        try:
            return self.scheduled_events_causing_resets[current_eval_time].relevant_proc_time_for_scheduled
        except:
            return self._DEFAULT_START_DATE


    # TODO: This will eventually need to be changed in functionality when https://ideasinc.atlassian.net/browse/HEISEN-4618 reaches completion
    def check_time_stamp_for_scheduled_processing(self, eval_time: pd.Timestamp)->bool:
        return eval_time in self.scheduled_events_causing_resets.keys()

    def is_eval_time_within_30_min_of_a_scheduled_processing(self, eval_time: pd.Timestamp):
        # TODO: verify that it is 30 minutes
        # minProcTime = np.min([abs((evalTime-procTime)).total_seconds() for procTime in self.scheduledEventsCausingResets.keys()])
        min_proc_time = np.min([abs((eval_time - procTime)).total_seconds() for procTime in self.scheduled_process_times])
        # minProcTime = min(minProcTime,minProcTime2)
        return min_proc_time <= 30 * 60





