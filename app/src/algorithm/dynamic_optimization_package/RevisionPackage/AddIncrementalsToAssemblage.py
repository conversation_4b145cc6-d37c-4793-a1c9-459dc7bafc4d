import pandas as pd

from src.algorithm.dynamic_optimization_package.RevisionPackage.ColumnsContainer import RevisionColumnsContainer
from src.algorithm.dynamic_optimization_package.RevisionPackage.ProcessDescriptionContainer import ProcessInfoDO,ProcessDescription
from dataclasses import dataclass
import numpy as np
import datetime
import pytz

@dataclass
class AddIncrementalsToAssemblage:
    rc:RevisionColumnsContainer
    _is_reset = 'isReset'
    _evaluation = 'evaluation'
    _event_time = 'eventTime'
    _processing_type = 'processingType'
    _time_split = 'timeSplit'
    _prev_event_time = 'prevEventTime'
    _keep_row = 'keepRow'
    _prev_event = 'prevEvent'
    _follows_reset_event = 'followsResetEvent'
    _prev_delta_solds = 'prevDeltaSolds'
    _shift_delta_solds = 'shiftDeltaSolds'

    # note not currently in use because the input has this preprocessed. Final version will not be most likely
    def convert_to_gmt(self,local_dt, time_zone):
        local_dt = datetime.strptime(local_dt, '%Y-%m-%d %H:%M:%S')
        local_tz = pytz.timezone(time_zone)
        local_dt = local_tz.localize(local_dt)
        return local_dt

    def assembly_file_expander(self,assem:pd.DataFrame):
        '''
        The assembly file is occasionally missing some evaluations for some accom Class. So we need to carry them forward
        :param assem:
        :return:
        '''
        assem = assem.sort_values(self.rc.evalTimeCol,ignore_index=True)
        accomClasses = assem[self.rc.accomClassCol].unique().tolist()
        currentState = {accomClass:None for accomClass in accomClasses}
        stateList = []
        for evalTime, evaldf in assem.groupby(self.rc.evalTimeCol):
            newState = self.turn_df_into_dict_by_accom_class(evaldf)
            currentState = self.update_state(currentState=currentState,newState=newState,newEvalTime=evalTime)
            stateList.extend([state for state in currentState.values()])
        return pd.concat(stateList,ignore_index=True)


    def update_state(self,currentState:dict,newState:dict,newEvalTime:pd.Timestamp):
        # copy the new data
        for accomClass,accomdf in newState.items():
            currentState[accomClass] = accomdf

        # update all of the time stamps
        for accomdf in currentState.values():
            if not accomdf is None:
                accomdf[self.rc.evalTimeCol] = newEvalTime
        return currentState

    def turn_df_into_dict_by_accom_class(self,indf):
        return {accomClass:accomdf.copy() for accomClass,accomdf in indf.groupby(self.rc.accomClassCol)}

    def preprocess_data(self, assem: pd.DataFrame, input_process: pd.DataFrame, process_time_col: str):

        assem_date_cols = [self.rc.dayCol,self.rc.evalTimeCol,self.rc.caughtUpDateCol]
        input_process_date_cols = [process_time_col]
        assem[assem_date_cols] = assem[assem_date_cols].apply(pd.to_datetime)
        input_process[input_process_date_cols] = input_process[input_process_date_cols].apply(pd.to_datetime)

        # if we have more than one room class we may be missing some from the evaluations since evaluations only contain ones with changes
        if assem[self.rc.accomClassCol].nunique()>1 and False: # TODO: Reenable this at some point. Idk, why its not working with BKKWA
            assem = self.assembly_file_expander(assem=assem)

        assem = assem.sort_values(by=[self.rc.accomClassCol, self.rc.evalTimeCol, self.rc.dayCol],ignore_index=True)
        input_process = input_process.sort_values(by=[process_time_col], ignore_index=True)
        input_process[self._is_reset] = 1

        return assem, input_process

    def generate_events(self, assem: pd.DataFrame, input_process: pd.DataFrame, process_time_col: str, opt_type_col: str):
        # what to do if we have evaluation and process at the same time?
        proc_iterator = zip(input_process[process_time_col].to_list(),input_process[opt_type_col].to_list())
        proc_times = [[instance_time, opt_type] for instance_time, opt_type in proc_iterator]
        eval_times = [[instance_time, self._evaluation] for instance_time in assem[self.rc.evalTimeCol].unique()]
        event_times = [*proc_times, *eval_times]
        return pd.DataFrame(event_times, columns=[self._event_time, self._processing_type]).sort_values([self._event_time], ignore_index=True)

    def filter_events_under_one_minute(self, events: pd.DataFrame):
        events[self._time_split] = (events[self._event_time] - events[self._event_time].shift(1)).apply(lambda x: x.total_seconds())
        events[self._prev_event_time] = events[self._event_time].shift(1)
        end_indices = events.query(f'{self._time_split}<=60').index.tolist()
        start_indices = np.array(events.query(f'{self._time_split}<=60').index.tolist())-1
        if len(end_indices) == 0: return events
        events[self._keep_row] = True
        input_process_types = [ProcessDescription.BDE.value, ProcessDescription.CDP.value, ProcessDescription.CDP_ON_DEMAND.value]
        proc_series = events[self._processing_type].values
        prev_processes = proc_series[start_indices]
        curr_processes = proc_series[end_indices]
        mask = np.isin(curr_processes, input_process_types) & ~np.isin(prev_processes, input_process_types)
        events.loc[np.array(end_indices)[mask], self._keep_row] = False
        events.loc[np.array(start_indices)[mask], self._processing_type] = np.array(curr_processes)[mask]
        events = events[events[self._keep_row]].reset_index(drop=True).drop([self._keep_row, self._time_split], axis=1)
        return events

    def add_incrementals_to_assemblage(self, assem:pd.DataFrame, input_process:pd.DataFrame, process_time_col:str= "processingTime", opt_type_col:str= "input_type"):

        assem, input_process = self.preprocess_data(assem, input_process, process_time_col)
        events = self.generate_events(assem, input_process, process_time_col, opt_type_col)
        events = self.filter_events_under_one_minute(events)

        events[self._prev_event_time] = events[self._event_time].shift(1)
        events[self._prev_event] = events[self._processing_type].shift(1).fillna('null')
        events[self._follows_reset_event] = (events[self._prev_event].apply(ProcessDescription).isin(ProcessInfoDO.__ProcsForUsingFullWindow__)  ) * 1

        scheduled_events_only = events.query(f'{self._processing_type} in ("{ProcessDescription.BDE.value}","{ProcessDescription.CDP.value}", "{ProcessDescription.CDP_ON_DEMAND.value}")').reset_index(drop=True)[self._event_time].to_list()

        # need to use original assem due to needing previous values from before evaluation Day
        merged_assem = assem.merge(events[[self._event_time, self._follows_reset_event,self._prev_event, self._prev_event_time]]
                                   .rename({self._event_time: self.rc.evalTimeCol}, axis=1),how='left', on=self.rc.evalTimeCol)
        merged_assem = merged_assem.sort_values([self.rc.accomClassCol, self.rc.dayCol, self.rc.evalTimeCol],
                                                ignore_index=True,ascending=[True, True, True])
        merged_assem[self._prev_delta_solds] = merged_assem.groupby([self.rc.accomClassCol, self.rc.dayCol])[self.rc.deltaSoldsCol].shift(1)
        merged_assem[self._shift_delta_solds] = merged_assem[self.rc.deltaSoldsCol] - merged_assem[self._prev_delta_solds]

        # TODO, when we have an opt event we need to update the correct window. This is waiting on https://ideasinc.atlassian.net/browse/HEISEN-4618
        merged_assem[self.rc.incSoldsCol] = np.where(
            merged_assem[self._follows_reset_event].isna() | (merged_assem[self._follows_reset_event] == 0),
            merged_assem[self._shift_delta_solds],
            merged_assem[self.rc.deltaSoldsCol]
        )

        min_proc_time = pd.to_datetime(input_process[process_time_col]).min().date()
        filtered_assem = merged_assem.query(f'{self.rc.evalTimeCol}>=@min_proc_time').reset_index(drop=True)
        return filtered_assem, scheduled_events_only



