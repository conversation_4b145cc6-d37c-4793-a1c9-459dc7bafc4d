from enum import Enum


class AdjustmentReason(Enum):
    potential_too_high_decrease_percentile= "potential_too_high_decrease_percentile"
    potential_too_low_increase_percentile= "potential_too_low_increase_percentile"
    potential_too_high_oob_decrease_percentile= "potential_too_high_oob_decrease_percentile"
    potential_too_low_oob_increase_percentile= "potential_too_low_oob_increase_percentile"
    potential_out_of_bounds= "potential_out_of_bounds"
    potential_in_spec= 'potential_in_spec'
    is_override_target_num_opts= 'is_override_target_num_opts'
    boundary_violation_recommends_too_low='boundary_violation_recommends_too_low'
    boundary_violation_recommends_too_high='boundary_violation_recommends_too_high'

    # the special conditions here are mediated by both deltaLRV and lrv changes being
    # out of spec
    special_increase_percentile_condition = 'special_increase_percentile_condition'
    special_decrease_percentile_condition = 'special_decrease_percentile_condition'

    # moving in the right direction keeps us out of spec
    unsatisfiable_conditions_when_increasing_percentile= 'unsatisfiable_conditions_when_increasing_percentile'
    unsatisfiable_conditions_when_decreasing_percentile= 'unsatisfiable_conditions_when_decreasing_percentile'



class ControlsConditions(Enum):
    delta_lrv_in_spec='delta_lrv_in_spec'
    delta_lrv_high='delta_lrv_high'
    delta_lrv_low='delta_lrv_low'
    lrv_high='lrv_high'
    lrv_in_spec='lrv_in_spec'
    lrv_low='lrv_low'

    @classmethod
    def determine_lrv_condition(cls, med_max_shift_lrv, min_shift_lrv_thresh, max_shift_lrv_thresh):
        if med_max_shift_lrv < min_shift_lrv_thresh:
            lrv_condition = cls.lrv_low
        elif med_max_shift_lrv>max_shift_lrv_thresh:
            lrv_condition = cls.lrv_high
        else:
            lrv_condition = cls.lrv_in_spec
        return lrv_condition

    @classmethod
    def determine_delta_lrv_condition(cls, med_max_shift_delta_lrv, min_shift_delta_lrv_thresh, max_shift_delta_lrv_thresh):
        if med_max_shift_delta_lrv < min_shift_delta_lrv_thresh:
            delta_lrv_condition = cls.delta_lrv_low
        elif med_max_shift_delta_lrv > max_shift_delta_lrv_thresh:
            delta_lrv_condition = cls.delta_lrv_high
        else:
            delta_lrv_condition = cls.delta_lrv_in_spec
        return delta_lrv_condition



