from dataclasses import dataclass


@dataclass
class RevisionColumnsContainer:
    dayCol: str
    evalTimeCol: str
    accomClassCol: str
    lrvCol: str
    priceCol: str
    deltaLRVCol: str
    deltaSoldsCol: str
    incSoldsCol: str
    ceilCol: str
    refRateCol: str
    lrvCol: str
    availCapCol:str
    ltCol:str='lt'
    caughtUpDateCol:str='caughtUpDate'
    #calibPotentialCol: str = 'calibPotential'


    def getColList(self):
        """
        simple function to return all columns for efficiency purposes. Allows me to subset a dataframe quickly
        :return:
        """
        return list(self.__dict__.values())[:-2] # I don't want to return the leadtime column here