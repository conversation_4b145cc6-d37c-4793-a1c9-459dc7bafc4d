import logging
from cgitb import reset
from dataclasses import dataclass
import datetime
import pandas as pd
import numpy as np
import copy
import pickle

from pyarrow import show_info

from src.algorithm.dynamic_optimization_package.RevisionPackage.DayDO import <PERSON>DO
from src.algorithm.dynamic_optimization_package.RevisionPackage.ColumnsContainer import RevisionColumnsContainer
from src.algorithm.Potentials.Potentials import PotentialMethods
from src.algorithm.dynamic_optimization_package.CommonDynamicOptElements import CommonDOElements
from src.algorithm.dynamic_optimization_package.RevisionPackage.EvalChangeDO import Eval<PERSON>hangeD<PERSON>
from src.algorithm.dynamic_optimization_package.RevisionPackage.fast_deep_copy import fast_deepcopy
# thoughts. Building the dataframe every single time will be difficult.


"""
This class is intended to hold the current state of the simulation
"""

@dataclass
class RewriteHistorySimulation:

    logger = logging.getLogger(__name__)
    current_state:dict[float | str | int,dict[pd.Timestamp,DayDO]] # accomClass, stayDate
    current_sim_time:pd.Timestamp
    rc:RevisionColumnsContainer
    override_to_full_window_update:bool=False
    min_num_seconds_between_opts: int | float | None = None
    _DEFAULT_OPT_TIME = pd.to_datetime('1969-08-15')
    _DEFAULT_MAX_OPT_DATE = pd.to_datetime('2125-01-01')
    _is_opt = 'isOpt'
    _opt_type = 'optType'
    _max_date = 'maxDate'
    _is_reset = 'isReset'
    _do_nothing = 'Do Nothing'
    _scheduled = 'Scheduled'
    _scheduled_or_triggered = 'Scheduled/Triggered'
    _is_count_restricted = 'isCountRestricted'
    _triggered = 'Triggered'
    _day_count = 'dayCount'
    _max_opt_restricted = 'maxOptRestricted'
    _time_restricted = 'Time Restricted'
    __debug_mode__:bool=False

    def simulate(self, ECDO:EvalChangeDO, test_potentials:dict
                 , potential_mode:str = 'slidingwindow', window_size:int=21
                 , use_leading_window:bool=False, fail_on_missing_occupancy_dates:bool=False, include_scheduled_processings:bool=False
                 , max_num_dyn_opts_per_day: int | float | None=None):
        evaluation_list = ECDO.get_sorted_evaluation_time()[1:] # initial State was built out of the 0th element
        unique_caught_up_dates = list(set([pd.to_datetime(x.date()) for x in ECDO.get_sorted_evaluation_time()]))
        daily_dyn_opt_counter = {day:dict(dayCount=0,isCountRestricted=False) for day in unique_caught_up_dates}
        scheduled_queue = [x for x in ECDO.scheduled_process_times if x > self.current_sim_time]
        opt_list = []
        opt_details = []
        scheduled_counter=0
        last_opt_time = self._DEFAULT_OPT_TIME # make it something a really long time ago for the initial state
        if self.__debug_mode__:
            assem_list = []
        for eval_time in evaluation_list:
            is_opt, max_opt_date, opt_type, scheduled_counter, daily_dyn_opt_counter, last_opt_time , is_reset, scheduled_queue = self.evaluate_outcome_of_current_state(
                eval_time=eval_time, last_opt_time=last_opt_time, ECDO=ECDO, scheduled_counter=scheduled_counter, test_potentials=test_potentials
            , potential_mode=potential_mode, window_size=window_size, use_leading_window=use_leading_window
                , fail_on_missing_occupancy_dates=fail_on_missing_occupancy_dates, daily_dyn_opt_counter=daily_dyn_opt_counter
                , max_num_dyn_opts_per_day=max_num_dyn_opts_per_day, scheduled_queue=scheduled_queue
            )
            if self.__debug_mode__:
                assem = self.generate_assemblage_file_from_current_state()
                assem[self._is_opt]=True
                assem[self._opt_type] = opt_type
                assem_list.append(assem)
            opt_list.append(is_opt)
            opt_details.append([self.current_sim_time, is_opt, max_opt_date, opt_type, is_reset])

            # update the state based on current State
            inc_state = ECDO.get_incremental_state(eval_time)
            min_date,max_date = ECDO.get_stay_range_for_eval_date(eval_time)
            self.update_simulation_state(state_update_info=inc_state, last_was_opt=is_reset, min_date=min_date, max_opt_date=max_opt_date, new_eval_time=eval_time)

        # evaluate the final state
        is_opt, max_opt_date, opt_type, scheduled_counter, daily_dyn_opt_counter, last_opt_time, is_reset, scheduled_queue = self.evaluate_outcome_of_current_state(
            eval_time=eval_time, last_opt_time=last_opt_time, ECDO=ECDO, scheduled_counter=scheduled_counter,
            test_potentials=test_potentials
            , potential_mode=potential_mode, window_size=window_size, use_leading_window=use_leading_window
            , fail_on_missing_occupancy_dates=fail_on_missing_occupancy_dates, daily_dyn_opt_counter=daily_dyn_opt_counter
            , max_num_dyn_opts_per_day=max_num_dyn_opts_per_day, scheduled_queue=scheduled_queue
        )
        if self.__debug_mode__:
            assem = self.generate_assemblage_file_from_current_state()
            assem[self._is_opt] = True
            assem[self._opt_type] = opt_type
            assem_list.append(assem)
            assem = pd.concat(assem_list,ignore_index=True)
        opt_list.append(is_opt)
        opt_details.append([self.current_sim_time, is_opt, max_opt_date, opt_type, is_reset])
        # Ok, If I want this to be purely targeting the number of dynamic opts I need to first subtract
        if include_scheduled_processings:
            n_opts = np.sum(opt_list)
        else:
            n_opts = np.sum(opt_list)#-scheduledCounter
        opt_details_df = pd.DataFrame(opt_details,columns=[self.rc.evalTimeCol,self._is_opt,self._max_date,self._opt_type,self._is_reset])
        return n_opts, opt_details_df


    def evaluate_outcome_of_current_state(self, eval_time:pd.Timestamp, last_opt_time:pd.Timestamp, ECDO:EvalChangeDO, scheduled_counter, test_potentials
                                          , potential_mode, window_size, use_leading_window, fail_on_missing_occupancy_dates, daily_dyn_opt_counter, max_num_dyn_opts_per_day, scheduled_queue):
        is_reset=False
        is_scheduled = False
        opt_type = self._do_nothing
        if  any([x <= eval_time for x in scheduled_queue]):
            is_reset=True
            max_opt_date = self._DEFAULT_MAX_OPT_DATE #ECDO.getScheduledProcessingEndDate(evalTime)
            opt_type = self._scheduled
            last_opt_time = np.max([x for x in scheduled_queue if x <= eval_time])
            scheduled_counter += 1
            is_scheduled=True
            scheduled_queue =[x for x in scheduled_queue if x > eval_time]

        if not self.min_num_seconds_between_opts is None:
            is_too_soon_since_last_opt = (eval_time - last_opt_time).total_seconds() <= self.min_num_seconds_between_opts
            is_too_close_to_scheduled = ECDO.is_eval_time_within_30_min_of_a_scheduled_processing(eval_time)
            is_too_soon_to_call_opt = is_too_soon_since_last_opt or is_too_close_to_scheduled
        else:
            is_too_soon_to_call_opt = False


        if not is_too_soon_to_call_opt:
            # Select the most recent last_modified <= eval_time
            eval_time = eval_time.replace(tzinfo=datetime.timezone.utc)
            candidate_ts = [ts for ts in test_potentials.keys() if ts <= eval_time]

            if not candidate_ts:
                self.logger.debug(f'No candidate potentials found, choosing the latest available potentials')
                candidate_ts.append(max(test_potentials.keys()))

            selected_ts = max(candidate_ts)
            curr_potential = test_potentials[selected_ts]
            is_opt, max_opt_date_eval = self.evaluate_current_state(test_potentials=curr_potential,
                                                                    potential_mode=potential_mode, window_size=window_size,
                                                                    use_leading_window=use_leading_window,
                                                                    fail_on_missing_occupancy_dates=fail_on_missing_occupancy_dates)
            if not is_scheduled:
                max_opt_date = max_opt_date_eval
            if is_opt:
                is_reset=True
                if not daily_dyn_opt_counter[pd.to_datetime(eval_time.date())][self._is_count_restricted]:
                    if is_scheduled:
                        opt_type = self._scheduled_or_triggered
                    else:
                        opt_type = self._triggered
                    last_opt_time = eval_time
                    if not max_num_dyn_opts_per_day is None:
                        daily_dyn_opt_counter[pd.to_datetime(eval_time.date())][self._day_count] += 1
                        if daily_dyn_opt_counter[pd.to_datetime(eval_time.date())][self._day_count] >= max_num_dyn_opts_per_day:
                            daily_dyn_opt_counter[pd.to_datetime(eval_time.date())][self._is_count_restricted] = True
                else:
                    opt_type = self._max_opt_restricted
                    is_opt = False
                    max_opt_date = None
        else:
            is_opt = False
            max_opt_date = None
            opt_type = self._time_restricted
        return is_opt, max_opt_date, opt_type, scheduled_counter, daily_dyn_opt_counter, last_opt_time, is_reset, scheduled_queue
        # optList.append(is_opt)
        # optDetails.append([self.current_sim_time, is_opt, max_opt_date, opt_type])
        # # update the state
        # incState = ECDO.get_incremental_state(eval_time)
        # minDate, maxDate = ECDO.get_stay_range_for_eval_date(eval_time)


    @classmethod
    def build_simulation_initial_state_from_data_frame(cls, assemblage:pd.DataFrame, rc:RevisionColumnsContainer,overrideToFullWindowUpdate:bool=False,minNumSecondsBetweenOpts:int|float|None=None):
        """
        This will take a single dataframe as an input and build the initial simulation state out of it
        :param assemblage:
        :param rc:
        :return:
        """
        data = {accomClassId:{} for accomClassId in assemblage[rc.accomClassCol].unique()}
        for row in assemblage.to_dict(orient='records'):
            newDay = DayDO(
                accom_class_id=row[rc.accomClassCol]
                , occupancy_date=row[rc.dayCol]
                , evaluation_time=row[rc.evalTimeCol]
                ,lrv=row[rc.lrvCol]
                , delta_lrv=row[rc.deltaLRVCol]
                , delta_solds=row[rc.deltaSoldsCol]
                , incremental_solds=row[rc.incSoldsCol]
                ,ceiling=row[rc.ceilCol]
                , price=row[rc.priceCol]
                , available_capacity=row[rc.availCapCol]
            )
            # add a day to the thing
            data[row[rc.accomClassCol]][row[rc.dayCol]]=newDay
        return RewriteHistorySimulation(current_state=data, rc=rc, override_to_full_window_update=overrideToFullWindowUpdate, min_num_seconds_between_opts=minNumSecondsBetweenOpts)

    def update_simulation_state(self, state_update_info:dict[float | str | int,dict[pd.Timestamp,DayDO]], new_eval_time:pd.Timestamp
                                , last_was_opt:bool, min_date:pd.Timestamp, max_opt_date: pd.Timestamp | None=None):
        '''
        Update the state of the simulation with the incrementals.
        :param state_update_info:
        :param last_was_opt: boolean indicating whether the last state produced an optimization
        :param min_date: basically the current system date. We want to delete anything before this
        :param max_opt_date: what was the largest date from the optimization
        :return:
        '''
        # may need to copy new accom classes in
        skippable_accom_class = []
        for accom_class in state_update_info.keys():
            if not accom_class in self.current_state.keys():
                self.current_state[accom_class] = fast_deepcopy(state_update_info[accom_class])
                skippable_accom_class.append(accom_class)

        # there may be cases where there is no update to a particular accom class for some reason but it does still exist
        no_update_accom_class = []
        for accom_class in self.current_state.keys():
            if not accom_class in state_update_info.keys():
                no_update_accom_class.append(accom_class)
        no_update_accom_class = set(no_update_accom_class)

        # delete expired elements
        for accom_class, accom_dict in self.current_state.items():
            deletion_list = [stay_date for stay_date in list(accom_dict.keys()) if stay_date < min_date]
            for delete_day in deletion_list:
                self.current_state[accom_class].pop(delete_day, None)


        # add new elements from the state info
        date_list = []
        for dl in self.current_state.values():
            date_list = [*date_list,*list(dl.keys())]
        try:
            local_max_date = np.max(date_list)
        except:
            self.logger.error(f'failed to generate max date list: {date_list}')

        # print('update bou8ndary finder')
        update_boundary_finder = []
        for accom_class, accom_dict in state_update_info.items():
            new_days = [day for day in accom_dict.keys() if day > local_max_date]
            for day in new_days:
                update_boundary_finder.append(day)
                self.current_state[accom_class][day] = fast_deepcopy(accom_dict[day])  # when this is first observed the incremental and the deltaSolds should be equal


        if max_opt_date is None:
            # this ensures we have the correct comparison datatype and also will never reset the elements
            max_opt_date = min_date - pd.Timedelta(value=1, unit='D')

        if self.override_to_full_window_update and last_was_opt:
            max_opt_date = pd.to_datetime('2125-12-31')

        # update elements
        # update boundary finder prevents us from updating new elements
        if update_boundary_finder:
            update_boundary = np.min(update_boundary_finder)  # we don't update elements beyond this point
        else:
            date_list = []
            for dl in state_update_info.values():
                date_list = [*date_list, *list(dl.keys())]
            local_max_date = np.max(date_list)
            update_boundary = local_max_date + pd.Timedelta(value=1,unit='D')

        # print('actualUpdate Phase')
        self.current_sim_time = new_eval_time
        for accom_class_id, accom_dict in self.current_state.items():
            if  accom_class_id in skippable_accom_class or (accom_class_id in no_update_accom_class and not last_was_opt):  # this will happen when we are observing a new accom class
                continue   # we don't need to update the elements
            for stay_date, accom_day_dict in accom_dict.items():
                if stay_date >= update_boundary:
                    continue  # cycling if the element is new. We don't want to do anything with it

                if last_was_opt and stay_date <= max_opt_date:
                    if accom_class_id in list(state_update_info.keys()) and not accom_class_id in no_update_accom_class:
                        try:
                            new_stay_date_info = fast_deepcopy(state_update_info[accom_class_id][stay_date])
                            # the delta solds would have been set to zero and we would have only captured the incremental solds from here.
                            accom_day_dict.delta_solds = new_stay_date_info.incremental_solds
                            accom_day_dict.lrv = new_stay_date_info.lrv
                            accom_day_dict.delta_lrv = new_stay_date_info.delta_lrv
                            accom_day_dict.ceiling = new_stay_date_info.ceiling
                            accom_day_dict.price = new_stay_date_info.price
                        except KeyError as e:
                            # we didnt have a new state to update. Just set the delta solds to zero
                            accom_day_dict.delta_solds=0.
                    else:
                        # if the accomclass didn't exist, we still triggered an update, but the deltaLRV,LRV, etc... wouldn't have changed
                        # conceivably, so we just set the deltasolds to zero. I am not clear on why this case can happen, but it happens
                        accom_day_dict.delta_solds = 0.
                else:
                    if not accom_class_id in no_update_accom_class:
                        # TODO: Figure out why this is sometimes missing when the expander is used
                        accom_day_dict.delta_solds += state_update_info[accom_class_id][stay_date].incremental_solds
                        if not np.isnan(accom_day_dict.available_capacity):
                            accom_day_dict.available_capacity -= state_update_info[accom_class_id][stay_date].incremental_solds

# TODO: Is it that the incremental solds need to be lagged some how?





    def generate_assemblage_file_from_current_state(self)->pd.DataFrame:
        newRows = []
        for  accomClassDict in self.current_state.values():
            for  dayState in accomClassDict.values():
                newRows.append(dayState.return_as_row())
        newAssemblage = pd.DataFrame(newRows, columns=DayDO.get_row_col_order(self.rc))
        # need to calculate the leadtime
        minDate = newAssemblage[self.rc.dayCol].min()
        newAssemblage[self.rc.ltCol] = (newAssemblage[self.rc.dayCol]-minDate).dt.days
        newAssemblage[self.rc.evalTimeCol] = self.current_sim_time
        return newAssemblage


    def evaluate_current_state(self, test_potentials:dict[float | int | str,float], potential_mode:str = 'slidingwindow',
                               window_size:int=21, use_leading_window:bool=False, fail_on_missing_occupancy_dates:bool=False):
        assemblage = self.generate_assemblage_file_from_current_state()
        assemblage['unused1'] = 0.0
        cap_date = assemblage[self.rc.dayCol].min()

        # need to do these by rc
        results = {}
        for accom_class_id, accom_class_df in assemblage.groupby(self.rc.accomClassCol):
            ac_assemblage, is_sufficient_data = CommonDOElements.computeAssemblageFile(dateCol = self.rc.dayCol,
                                                                                    assemblage=accom_class_df, ceilCol=self.rc.ceilCol, deltaOccCol=self.rc.deltaSoldsCol, lrvCol=self.rc.lrvCol
                                                                                    , refRateCol=self.rc.refRateCol, deltaLRVCol=self.rc.deltaLRVCol, capDate=cap_date, ltCol=self.rc.ltCol, rcacCol=self.rc.accomClassCol
                                                                                    , windowSize=window_size, percChangeInRemainingCapacityCol='unused1', use_leading_window=use_leading_window
                                                                                    , fail_on_missing_occupancy_dates=fail_on_missing_occupancy_dates)

            results[accom_class_id] = PotentialMethods.evaluatePotential(
                potentialMode=potential_mode,
                assemblage=ac_assemblage,
                dateCol=self.rc.dayCol,
                thresholdPotential=test_potentials[accom_class_id],
                hasSufficientData=is_sufficient_data,
                debugMode=False,
                pressure_floor=-1.,
                occChange_threshold=0.,
                occChangeCol='__rollingMaxPercChangeOcc__'
            )
        is_opt = any([val[0] for val in results.values()])
        if is_opt:
            max_date = np.max([val[1] for val in results.values() if not val[1] is None])
            return True, max_date
        return False, None
