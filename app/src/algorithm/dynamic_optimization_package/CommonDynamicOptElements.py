import numpy as np
import pandas as pd
import datetime
from src.algorithm.Utilities.GenericUtilities.GenericDeltaLRVHandler import GenericDeltaLRVHandler
from src.algorithm.Utilities.GenericUtilities.GenericDeltaOccHandler import GenericDeltaOccHandler
from src.algorithm.Utilities.GenericUtilities.GenericRefRateHandler import GenericRefRateHandler
from src.algorithm.Utilities.GenericUtilities.fastDateRange import fastDateRange


class CommonDOElements:
    __insufficientDataThresholdPotential__ = 99999999999.

    def get_intersection(self, s: list) -> list:
        """ return an intersection from lrvProcTimes set of lists """
        i = set(s[0])
        for x in s[1:]:
            i = i & set(x)
        return list(i)

    @staticmethod
    def computeAssemblageFile(assemblage:pd.DataFrame,ceilCol:str,deltaOccCol:str,lrvCol:str,dateCol:str,refRateCol:str
                              ,deltaLRVCol:str,capDate:datetime.date|pd.Timestamp,ltCol:str,rcacCol:str,windowSize:int
                              , percChangeInRemainingCapacityCol:str, use_leading_window:bool=False
                              , fail_on_missing_occupancy_dates:bool=False):
        passesDataSufficiency = True
        assemblage['__occCeil__'] = assemblage[[ceilCol, deltaOccCol]].min(axis=1)
        assemblage['__adjLRV__'] = assemblage[lrvCol] + assemblage['__occCeil__'] * assemblage[
            deltaLRVCol]

        # max(0,adjLRV) so we never have resultant LRV<0
        assemblage['__dummyZero__'] = 0.
        assemblage['__intermediateLRV__'] = assemblage[['__dummyZero__', '__adjLRV__']].max(axis=1)

        # final Change in LRV
        assemblage['__finalChangeLRV__'] = assemblage['__intermediateLRV__'] - assemblage[lrvCol]
        assemblage['ai'] = assemblage['__finalChangeLRV__'] / assemblage[refRateCol]

        assemblage['ai2'] = np.power(assemblage['ai'], 2.)
        # there may be missing dates in between
        minObsDate = assemblage[dateCol].min()
        maxObsDate = assemblage[dateCol].max()
        expectedRows = (pd.to_datetime(maxObsDate) - pd.to_datetime(minObsDate)).days + 1
        if len(assemblage) > 0 and len(assemblage) != expectedRows:
            if not fail_on_missing_occupancy_dates:
                dateList = [day for day in
                            fastDateRange.fastDateRange(startDate=minObsDate, endDate=maxObsDate, endInclusive=True)]
                # get capdate
                capDate = pd.to_datetime(capDate)
                templateData = pd.DataFrame(dateList, columns=[dateCol])
                templateData[ltCol] = (
                        pd.to_datetime(templateData[dateCol]) - pd.to_datetime(capDate)).dt.days
                assemblage = templateData.merge(assemblage, how='left',
                                                on=[dateCol, ltCol]
                                                ).sort_values(by=[ltCol], ascending=[True],
                                                              ignore_index=True)
            else:
                raise Exception(f"Missing Occupancy Date found {capDate}")

        assemblage['ai2'] = assemblage['ai2'].replace([np.inf, -np.inf], np.nan).fillna(0.)
        # assemblage = assemblage.query('ai2>0.')
        # If all are zero then we can exit the calculation prematurely
        if len(assemblage) < windowSize:
            # create required columns in empty dataframe
            assemblage['pressure'] = np.nan
            assemblage['potential'] = np.nan
            passesDataSufficiency = False
            return assemblage, passesDataSufficiency
        # now make sure everything is sorted, since we are on lrvProcTimes single capdate its safe to
        # use the lt alone
        assemblage = assemblage.sort_values([rcacCol, ltCol],
                                            ascending=[True, not use_leading_window], ignore_index=True)
        assemblage['pressure'] = (
            assemblage.groupby(rcacCol, as_index=False)['ai2'].rolling(window=windowSize,
                                                                                       min_periods=windowSize).sum()[
                'ai2'])

        assemblage[percChangeInRemainingCapacityCol] = assemblage[percChangeInRemainingCapacityCol].astype(float).fillna(0.)
        assemblage['__rollingMaxPercChangeOcc__'] = (assemblage
            .groupby(rcacCol, as_index=False)[percChangeInRemainingCapacityCol]
            .rolling(window=windowSize,min_periods=windowSize).max()[percChangeInRemainingCapacityCol])
        return assemblage, passesDataSufficiency


    def build_individual_stuff(self, deltaocchandler: GenericDeltaOccHandler, deltalrvhandler: GenericDeltaLRVHandler,
                               refRateHandler: GenericRefRateHandler, use_leading_window=False,
                               fail_on_missing_occupancy_dates=True,windowSize:int=21):

        # create the initial assemmblage state
        joinCols = [deltaocchandler.rcacCol, deltaocchandler.dateCol, deltaocchandler.ltCol]
        renameDict = dict(rcCol=deltaocchandler.rcacCol, datecol=deltaocchandler.dateCol, ltCol=deltaocchandler.ltCol)
        assemblage = deltalrvhandler.loadDataRenamingCols(**renameDict).merge(
            refRateHandler.loadDataRenamingCols(**renameDict), how='left', on=joinCols).merge(
            deltaocchandler.loadDataRenamingCols(), how='left', on=joinCols
        )

        # clean up the assemlage dataset
        assemblage[deltalrvhandler.capDateCol] = deltaocchandler.capDate
        assemblage[deltaocchandler.deltaOccCol] = assemblage[deltaocchandler.deltaOccCol].replace([np.inf, -np.inf], np.nan).fillna(0.)

        # make all of the calculations for it
        assemblage, passesDataSufficiency = CommonDOElements.computeAssemblageFile(
            assemblage=assemblage, ceilCol=deltalrvhandler.ceilCol, lrvCol= deltalrvhandler.lrvCol, deltaOccCol=deltaocchandler.deltaOccCol
            , dateCol=deltaocchandler.dateCol , refRateCol= refRateHandler.refRateCol, deltaLRVCol= deltalrvhandler.deltaLRVCol
            , capDate=pd.to_datetime(deltaocchandler.capDate), ltCol= deltaocchandler.ltCol, rcacCol=deltaocchandler.rcacCol
            , windowSize=windowSize, percChangeInRemainingCapacityCol = deltaocchandler.percChangeInRemainingCapacityCol
            , use_leading_window=use_leading_window, fail_on_missing_occupancy_dates=fail_on_missing_occupancy_dates
        )

        return assemblage, passesDataSufficiency
