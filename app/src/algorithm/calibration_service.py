from typing import Callable

import pandas as pd

from src.algorithm.Utilities.GenericUtilities.GenericObjFileLoaders import GenericDeltaLoader
from src.algorithm.dynamic_optimization_package.CalibrateDynamicOptimization import CalibrationDynamicOptimization
from src.algorithm.dynamic_optimization_package.CalibrationAdjustment import guess_percentile, \
    get_new_threshold_from_fun
from src.common.dto.calibrated_potential import CalibratedPotential


def run_calibration(file_reader, delta_occ_solds, reference_rate, delta_lrv, window_size, min_data_req, percentile,
                    use_leading_window, fail_on_missing_occ_dates, pressure_floor):
    occupancy_manager = GenericDeltaLoader.loadDeltaOccFromDeltaOccFile(filepath=delta_occ_solds,
                                                                        filereader=file_reader,
                                                                        defaultRC=-1,
                                                                        rcacCol='accomClassId',
                                                                        ltCol='leadTime', deltaOccCol='deltaSolds',
                                                                        dateCol='occupancyDate',
                                                                        capDateCol='captureDate',
                                                                        remainingCapacityCol='availableCapacity')
    ref_price_manager = GenericDeltaLoader.loadRefRateFile(filepath=reference_rate,
                                                           filereader=file_reader, defaultRC=-1, rcacCol='accomClassId',
                                                           ltCol='leadTime', refRateCol='price',
                                                           dateCol='occupancyDate', capDateCol='captureDate')
    lrv_delta_manager = GenericDeltaLoader.loadDeltaLRVFromDeltaLRVFile(filepath=delta_lrv,
                                                                        filereader=file_reader, defaultRC=-1,
                                                                        rcacCol='accomClassId',
                                                                        ltCol='leadTime', deltaLRVCol='deltaLrv',
                                                                        dateCol='occupancyDate',
                                                                        capDateCol='captureDate',
                                                                        ceilCol='ceilingValue',
                                                                        lrvCol='lrv')
    calibrator = CalibrationDynamicOptimization(deltaOccHandlerDict=occupancy_manager,
                                                refRateHandlerDict=ref_price_manager,
                                                deltaLRVHandlerDict=lrv_delta_manager,
                                                potentialMode='slidingwindow',
                                                windowSize=window_size,
                                                calibQuantile=percentile, useLeadingWindow=use_leading_window,
                                                failOnMissingOccupancyDates=fail_on_missing_occ_dates,
                                                pressure_floor=pressure_floor)
    calibrator.__toggleDebug__()
    calibrated, assemblage, quantile_fun = calibrator.calibrate()
    return [CalibratedPotential(accom_class_id=k, calibrated_potential=v) for k, v in
            calibrated.items()], assemblage, quantile_fun


def revise_threshold(waste_regret_df: pd.DataFrame, waste_threshold: int, regret_threshold: int,
                     threshold_func_by_rc: dict[int, Callable[[float], float]], current_calib_percentile: float,
                     user_waste_weight: float = 0.2, min_waste_regret_sum=5, max_calib_bound: float = 100,
                     min_calib_bound: float = 0) -> tuple[float, list[CalibratedPotential]]:
    revised_percentile = guess_percentile(
        wasteRegretDf=waste_regret_df, wasteThreshold=waste_threshold, regretThreshold=regret_threshold,
        currentCalibPercentile=current_calib_percentile,
        userWasteWeight=user_waste_weight, minDataReq=min_waste_regret_sum, accumulatedLRVCol='medianLrvChange',
        optCountCol='noOfOpt', maxCalibBound=max_calib_bound, minCalibBound=min_calib_bound)
    return revised_percentile, [CalibratedPotential(accom_class_id=rc,
                                                    calibrated_potential=get_new_threshold_from_fun(revised_percentile,
                                                                                                    threshold_fun)) for
                                rc, threshold_fun in threshold_func_by_rc.items()]


def compute_new_percentile(waste_regret_df: pd.DataFrame, waste_threshold: int, regret_threshold: int,
                           current_calib_percentile: float, user_waste_weight: float = 0.2,
                           min_waste_regret_sum=5,
                           max_calib_bound: float = 100, min_calib_bound: float = 0):
    calibrated_percentile = guess_percentile(
        wasteRegretDf=waste_regret_df, wasteThreshold=waste_threshold, regretThreshold=regret_threshold,
        currentCalibPercentile=current_calib_percentile,
        userWasteWeight=user_waste_weight, minDataReq=min_waste_regret_sum, accumulatedLRVCol='medianLrvChange',
        optCountCol='noOfOpt', maxCalibBound=max_calib_bound, minCalibBound=min_calib_bound)
    return calibrated_percentile
