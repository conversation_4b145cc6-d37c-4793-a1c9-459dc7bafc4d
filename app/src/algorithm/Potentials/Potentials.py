from datetime import date

import numpy as np
import pandas as pd


class PotentialMethods:
    @staticmethod
    def computePotential(potentialMode: str, assemblage: pd.DataFrame, dateCol: str,
                         pressure_floor: float = 0.,additionalCols:list|None=None) -> pd.DataFrame:
        """
        This will return a dataframe with [dateCol, 'potential']
        :param potentialMode:
        :param assemblage:
        :param dateCol:
        :return:
        """
        assemblage = assemblage.query('pressure > @pressure_floor').reset_index(drop=True)
        assemblage['potential'] = np.log(assemblage['pressure'])
        assemblage = assemblage.reset_index()
        if potentialMode == 'maximizing':
            totalPressure = assemblage.iloc[assemblage['potential'].argmax()][[dateCol, 'potential']]
        # elif potentialMode == 'shortwindow':
        #     subass = assemblage.query(f'{ltCol}<={windowSize}').reset_index(drop=True)
        #     transformer = {ltCol: (ltCol, 'max')}
        #     maximalizers = subass.groupby(rcacCol, as_index=False).agg(**transformer)
        #     totalPressure = subass.merge(maximalizers, how='inner', on=[rcacCol, ltCol])
        elif potentialMode == 'slidingwindow':
            if additionalCols is None:
                totalPressure = assemblage[[dateCol, 'potential']]
            else:
                totalPressure = assemblage[[dateCol, 'potential',*additionalCols]]
        else:
            raise RuntimeError('Potential mode for calibration is invalid')
        return totalPressure

    @staticmethod
    def evaluatePotential(potentialMode: str, assemblage: pd.DataFrame, dateCol: str, thresholdPotential: float,
                          hasSufficientData: bool, debugMode=False, pressure_floor: float = 0.
                          ,has_significant_occChange:bool=True,occChange_threshold:float=0.
                          ,occChangeCol:str='__rollingMaxPercChangeOcc__') -> (bool, date | None):
        if not hasSufficientData or not has_significant_occChange:
            return False, None



        totalPressure = PotentialMethods.computePotential(potentialMode=potentialMode, assemblage=assemblage,
                                                          dateCol=dateCol, pressure_floor=pressure_floor,additionalCols=[occChangeCol])
        tp = totalPressure.query(f"potential>=@thresholdPotential and {occChangeCol}>=@occChange_threshold")
        if not debugMode:
            if len(tp) < 1:
                # we have no successful
                return False, None
            return True, tp[dateCol].max()
        if len(tp) < 1:
            # we have no successful
            return False, None, tp
        return True, tp[dateCol].max(), tp
