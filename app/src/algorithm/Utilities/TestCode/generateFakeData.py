import pathlib
import numpy as np
import pandas as pd
from src.algorithm.Utilities.GenericUtilities.fastDateRange import fastDateRange
from datetime import date,timedelta
def generateFakeRefRate(template:pd.DataFrame,outPath : pathlib.Path):
    template = template.copy()
    template['refRate'] = np.random.uniform(80,130,size=len(template))
    template.to_csv(outPath/'refrate.csv',index=False)

def generateFakeDeltaOcc(template:pd.DataFrame,outPath : pathlib.Path):
    template = template.copy()
    template['deltaOcc'] = np.floor(np.random.uniform(-10,10,size=len(template)))
    template.to_csv(outPath/'deltaOcc.csv',index=False)
def generateFakeDeltaLRV(template:pd.DataFrame,outPath : pathlib.Path):
    template = template.copy()
    template['deltalrv'] = np.random.uniform(0.01,10,size=len(template))
    template.to_csv(outPath / 'deltalrv.csv',index=False)

def generateTemplate(startDate:date,endDate:date,maxlt:int):
    templateList = []
    for day in fastDateRange.fastDateRange(startDate=startDate,endDate = endDate):
        for lt in range(maxlt+1):
            templateList.append([day,lt,day-timedelta(days=lt)])
    return pd.DataFrame(templateList,columns=['staydate','lt','capturedate'])




def generateAndOutputFakeFiles():
    endDate = date.today()
    startDate = endDate - timedelta(days=364)
    maxlt = 180
    template = generateTemplate(startDate=startDate,endDate=endDate,maxlt=maxlt)
    template['rc'] = 1
    outPath = pathlib.Path("D:\\filetransferDir\\DynOptTestLocation\\calibFiles")
    generateFakeDeltaLRV(template=template,outPath=outPath)
    generateFakeDeltaOcc(template=template, outPath=outPath)
    generateFakeRefRate(template=template, outPath=outPath)
    maxCapDate = template['capturedate'].max()-timedelta(40)
    evalTemp = template.query('capturedate == @maxCapDate').reset_index(drop=True)
    outPath2 = pathlib.Path("D:\\filetransferDir\\DynOptTestLocation\\evalFiles")

    generateFakeDeltaLRV(template=evalTemp, outPath=outPath2)
    generateFakeDeltaOcc(template=evalTemp, outPath=outPath2)
    generateFakeRefRate(template=evalTemp, outPath=outPath2)
    ...


if __name__=="__main__":
    generateAndOutputFakeFiles()
