from src.algorithm.dynamic_optimization_package.CalibrateDynamicOptimization import CalibrationDynamicOptimization
from src.algorithm.dynamic_optimization_package.EvaluateDynamicOptimization import EvaluateDynamicOptimization
from src.algorithm.Utilities.GenericUtilities.GenericObjFileLoaders import GenericDeltaLoader
import pathlib
from datetime import date, timedelta
from timeit import timeit

def runCalib():
    basedir = pathlib.Path("D:\\filetransferDir\\potential\\calib\\")
    occman = GenericDeltaLoader.loadDeltaOccFromDeltaOccFile(filepath=basedir/'deltaOcc.parquet',
                                                    filereader=GenericDeltaLoader.__determineFileTypeReader__('deltaOcc.parquet')
                                                    ,defaultRC=-1
                                                    ,rcacCol='roomclassid'
                                                    ,ltCol='occLT'
                                                    ,deltaOccCol='delta'
                                                    ,dateCol='stayDate'
                                                    ,capDateCol='captureDate'
                                                    ,distinctRC=[1335,1336]
                                                    )

    refman = GenericDeltaLoader.loadRefRateFile(filepath=basedir/'refrate.parquet',
                                                    filereader=GenericDeltaLoader.__determineFileTypeReader__('refrate.parquet')
                                                    ,defaultRC=-1
                                                    ,rcacCol='roomclassid'
                                                    ,ltCol='lt'
                                                    ,refRateCol='refRate'
                                                    ,dateCol='staydate'
                                                    ,capDateCol='captureDate'
                                                , distinctRC=[1335, 1336]
                                                )

    lrvMan = GenericDeltaLoader.loadDeltaLRVFromDeltaLRVFile(filepath=basedir/'deltalrv.parquet',
                                                    filereader=GenericDeltaLoader.__determineFileTypeReader__('deltalrv.parquet')
                                                    ,defaultRC=-1
                                                    ,rcacCol='roomclassid'
                                                    ,ltCol='occLT'
                                                    ,deltaLRVCol='deltaLRV'
                                                    ,dateCol='stayDate'
                                                    ,capDateCol='captureDate'
                                                             , distinctRC=[1335, 1336]
                                                             )
    cdo = CalibrationDynamicOptimization(deltaOccHandlerDict=occman
                                   ,refRateHandlerDict=refman
                                   ,deltaLRVHandlerDict=lrvMan
                                   ,startCapDate=date(2024, 6, 25)-timedelta(182)
                                   ,endCapDate=date(2024, 6, 25)
                                   ,potentialMode='slidingwindow')
    calibrated, potentials = cdo.calibrate()
    import seaborn as sns
    import matplotlib.pyplot as plt
    sns.kdeplot(potentials,x='potential',hue='roomclassid')
    plt.show()
    return calibrated


def runEval(calibrated):
    basedir = pathlib.Path("D:\\filetransferDir\\DynOptTestLocation\\evalFiles")
    occman = GenericDeltaLoader.loadDeltaOccFromDeltaOccFile(filepath=basedir/'deltaOcc.csv',
                                                    filereader=GenericDeltaLoader.__determineFileTypeReader__('deltaOcc.csv')
                                                    ,defaultRC=-1
                                                    ,rcacCol='rc'
                                                    ,ltCol='lt'
                                                    ,deltaOccCol='deltaOcc'
                                                    ,dateCol='staydate'
                                                    ,capDateCol='capturedate'
                                                    )

    refman = GenericDeltaLoader.loadRefRateFile(filepath=basedir/'refrate.csv',
                                                    filereader=GenericDeltaLoader.__determineFileTypeReader__('refrate.csv')
                                                    ,defaultRC=-1
                                                    ,rcacCol='rc'
                                                    ,ltCol='lt'
                                                    ,refRateCol='refRate'
                                                    ,dateCol='staydate'
                                                    ,capDateCol='capturedate')

    lrvMan = GenericDeltaLoader.loadDeltaLRVFromDeltaLRVFile(filepath=basedir/'deltalrv.csv',
                                                    filereader=GenericDeltaLoader.__determineFileTypeReader__('deltalrv.csv')
                                                    ,defaultRC=-1
                                                    ,rcacCol='rc'
                                                    ,ltCol='lt'
                                                    ,deltaLRVCol='deltalrv'
                                                    ,dateCol='staydate'
                                                    ,capDateCol='capturedate')

    evaluator = EvaluateDynamicOptimization(
        deltaOccHandlerDict=occman
        ,deltaLRVHandlerDict=lrvMan
        ,refRateHandlerDict=refman
        ,calibrated=calibrated
        ,potentialMode='slidingwindow'
    )
    isrun = evaluator.evaluate()
    ...

def runEval2(occman,lrvMan,refman,calibrated):
    # approx 28% of the time is spent on this portion
    # timeit('runEval2(occman,lrvMan,refman,calibrated)', number=10_000, globals=globals())
    # 63.43150030000834
    evaluator = EvaluateDynamicOptimization(
        deltaOccHandlerDict=occman
        ,deltaLRVHandlerDict=lrvMan
        ,refRateHandlerDict=refman
        ,calibrated=calibrated
        ,potentialMode='slidingwindow'
    )
    isrun = evaluator.evaluate()

if __name__=="__main__":
    calibrated = runCalib()
    runEval(calibrated)

