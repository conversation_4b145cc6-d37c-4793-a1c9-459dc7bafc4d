import time

from src.algorithm.dynamic_optimization_package.CalibrateDynamicOptimization import CalibrationDynamicOptimization
from src.algorithm.dynamic_optimization_package.EvaluateDynamicOptimization import EvaluateDynamicOptimization
from src.algorithm.Utilities.GenericUtilities.GenericObjFileLoaders import GenericDeltaLoader
import pathlib
from datetime import date
import numpy as np

def loadCalib():
    basedir = pathlib.Path("D:\\filetransferDir\\DynOptTestLocation\\calibFiles")
    basedir = pathlib.Path("D:\\filetransferDir\\potential\\TraciCalibExample")
    occman = GenericDeltaLoader.loadDeltaOccFromDeltaOccFile(filepath=basedir / 'deltaOcc.csv',
                                                             filereader=GenericDeltaLoader.__determineFileTypeReader__(
                                                                 'deltaOcc.csv')
                                                             , defaultRC=-1
                                                             , rcacCol='rc'
                                                             , ltCol='lt'
                                                             , deltaOccCol='deltaOcc'
                                                             , dateCol='staydate'
                                                             , capDateCol='capturedate'
                                                             )

    refman = GenericDeltaLoader.loadRefRateFile(filepath=basedir / 'refrate.csv',
                                                filereader=GenericDeltaLoader.__determineFileTypeReader__('refrate.csv')
                                                , defaultRC=-1
                                                , rcacCol='rc'
                                                , ltCol='lt'
                                                , refRateCol='refRate'
                                                , dateCol='staydate'
                                                , capDateCol='capturedate')

    lrvMan = GenericDeltaLoader.loadDeltaLRVFromDeltaLRVFile(filepath=basedir / 'deltalrv.csv',
                                                             filereader=GenericDeltaLoader.__determineFileTypeReader__(
                                                                 'deltalrv.csv')
                                                             , defaultRC=-1
                                                             , rcacCol='rc'
                                                             , ltCol='lt'
                                                             , deltaLRVCol='deltalrv'
                                                             , dateCol='staydate'
                                                             , capDateCol='capturedate')
    return occman,refman,lrvMan

def runCalib(occman,refman,lrvMan):
    basedir = pathlib.Path("D:\\filetransferDir\\DynOptTestLocation\\calibFiles")

    cdo = CalibrationDynamicOptimization(deltaOccHandlerDict=occman
                                   ,refRateHandlerDict=refman
                                   ,deltaLRVHandlerDict=lrvMan
                                   ,startCapDate=date(2023, 1, 19)
                                   ,endCapDate=date(2024, 7, 15)
                                   ,potentialMode='slidingwindow'
                                    , maxDataLim=4)
    calibrated = cdo.calibrate()
    return calibrated



def loadEval():
    basedir = pathlib.Path("D:\\filetransferDir\\DynOptTestLocation\\evalFiles")
    occman = GenericDeltaLoader.loadDeltaOccFromDeltaOccFile(filepath=basedir/'deltaOcc.csv',
                                                    filereader=GenericDeltaLoader.__determineFileTypeReader__('deltaOcc.csv')
                                                    ,defaultRC=-1
                                                    ,rcacCol='rc'
                                                    ,ltCol='lt'
                                                    ,deltaOccCol='deltaOcc'
                                                    ,dateCol='staydate'
                                                    ,capDateCol='capturedate'
                                                    )

    refman = GenericDeltaLoader.loadRefRateFile(filepath=basedir/'refrate.csv',
                                                    filereader=GenericDeltaLoader.__determineFileTypeReader__('refrate.csv')
                                                    ,defaultRC=-1
                                                    ,rcacCol='rc'
                                                    ,ltCol='lt'
                                                    ,refRateCol='refRate'
                                                    ,dateCol='staydate'
                                                    ,capDateCol='capturedate')

    lrvMan = GenericDeltaLoader.loadDeltaLRVFromDeltaLRVFile(filepath=basedir/'deltalrv.csv',
                                                    filereader=GenericDeltaLoader.__determineFileTypeReader__('deltalrv.csv')
                                                    ,defaultRC=-1
                                                    ,rcacCol='rc'
                                                    ,ltCol='lt'
                                                    ,deltaLRVCol='deltalrv'
                                                    ,dateCol='staydate'
                                                    ,capDateCol='capturedate')
    return occman,refman,lrvMan
def runEval(calibrated,occman,refman,lrvMan):
    basedir = pathlib.Path("D:\\filetransferDir\\DynOptTestLocation\\evalFiles")


    evaluator = EvaluateDynamicOptimization(
        deltaOccHandlerDict=occman
        ,deltaLRVHandlerDict=lrvMan
        ,refRateHandlerDict=refman
        ,calibrated=calibrated
        ,potentialMode='slidingwindow'
    )
    isrun = evaluator.evaluate()
    ...

def runEval2(occman,lrvMan,refman,calibrated):
    # approx 28% of the time is spent on this portion
    # timeit('runEval2(occman,lrvMan,refman,calibrated)', number=10_000, globals=globals())
    # 63.43150030000834
    evaluator = EvaluateDynamicOptimization(
        deltaOccHandlerDict=occman
        ,deltaLRVHandlerDict=lrvMan
        ,refRateHandlerDict=refman
        ,calibrated=calibrated
        ,potentialMode='slidingwindow'
    )
    isrun = evaluator.evaluate()

def generateStatistics(func,args,nit):
    runtime = []
    for _ in range(nit):
        start = time.time()
        func(**args)
        end = time.time()
        runtime.append(end-start)

    # compute statistic
    mean= np.mean(runtime)
    median = np.median(runtime)
    std = np.std(runtime)
    max = np.max(runtime)
    min = np.min(runtime)
    totalRuntime = np.sum(runtime)
    print(f"func: {func.__name__}: {totalRuntime=}, {mean=}, {median=}, {std=}, {min=},{max=}")

if __name__=="__main__":

    #generateStatistics(loadCalib,{},10)
    occman, refman, lrvMan = loadCalib()
    #generateStatistics(runCalib,dict(occman=occman,refman=refman,lrvMan=lrvMan),nit=10)
    calibrated = runCalib(occman,refman,lrvMan)

    #generateStatistics(loadEval,{},10_000)
    occman,refman,lrvMan = loadEval()
    # nevals = 10_000
    # runtime = timeit('runEval(calibrated)',number=nevals,globals=globals())
    # print(f"{runtime=} for {nevals=}, avgRun = {runtime/nevals}")
    # runtime=161.38941010000417 for nevals=10000, avgRun = 0.016138941010000418
    # this is for my PC running off of an external drive.  72% of the processing time is spent on processing
    # input files. If we make the effort to reduce this then there will be a significant speed up.
    # for 1mill processing this will come out to about 1.75 hours on my machine
    # if we switch to polars best case scenario we'd be down to 36 minutes per day of processing time for 1mil evaluations
    # this is unlikely to be fully realized because I do not know how much time is actually spent in file loading.
    generateStatistics(runEval2,dict(calibrated=calibrated,occman=occman,refman=refman,lrvMan=lrvMan),nit=10_000)

    # deltaOcc = pd.DataFrame()
    # deltaOcc = deltaOcc[['stayDate','occLT','currentOB','finalOB']]
    # deltaOcc['stly'] = deltaOcc['stayDate']+timedelta(364)
    # renamedict = dict(stly='stayDate',finalOB='naiveFcst')
    # deltaOcc  = deltaOcc.merge(deltaOcc[['stly','finalOB']].rename(renamedict,axis=1)
    #                            ,how='left',on=['stayDate'])
    #
    # deltaOcc['ape'] = (deltaOcc['naiveFcst']/deltaOcc['finalOB']-1).abs()
    # deltaOcc.groupby('occLT',as_index=False).agg(MdAPE=('ape','median'))
