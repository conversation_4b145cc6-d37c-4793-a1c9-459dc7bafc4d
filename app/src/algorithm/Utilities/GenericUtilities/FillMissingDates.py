from datetime import datetime, timedelta
import pandas as pd

class FillMissingDates:

    def __init__(self):
        self.TODAY = datetime.now()
        self._NO_OF_DAYS_FOR_ZEROFILL_CHECK = 14

    def fill_missing_dates(self, df: pd.DataFrame, date_column: str):
        current_date = pd.Timestamp.now()
        min_date = df[date_column].min()
        max_date = df[date_column].max() if df[date_column].max().date() > current_date.date() else current_date
        date_diff = (max_date - min_date).days
        full_date_range = pd.date_range(start=min_date - timedelta(days=self._NO_OF_DAYS_FOR_ZEROFILL_CHECK - date_diff), end=max_date)
        df = df.set_index(date_column).reindex(full_date_range, fill_value=0).rename_axis(date_column).reset_index()
        return df
