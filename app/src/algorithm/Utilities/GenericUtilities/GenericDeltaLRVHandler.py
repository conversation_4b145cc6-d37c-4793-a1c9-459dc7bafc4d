from dataclasses import dataclass
import pandas as pd
from datetime import date,datetime, timedelta


@dataclass
class GenericDeltaLRVHandler:
    dataset:pd.DataFrame
    capDate: date | datetime | None = None  # this is maybe not important most of the time
    # columns now
    rcacCol: str = 'roomclass'  # room class or accom class
    ltCol: str = 'occLT'
    deltaLRVCol: str = 'deltaLRV'
    dateCol: str = 'occupancyDate'
    lrvCol: str = 'LRV'
    ceilCol:str='Ceiling_Value'
    capDateCol:str='capdate'

    def __post_init__(self):
        self.__correctDateCols__()
        self.dataset = self.dataset[[
            self.rcacCol, self.dateCol, self.ltCol, self.deltaLRVCol, self.lrvCol, self.ceilCol
        ]].sort_values([self.rcacCol, self.dateCol, self.ltCol], ascending=[True, True, True], ignore_index=True)

    def __correctDateCols__(self):
        for datecol in [self.capDateCol, self.dateCol]:
            self.dataset[datecol] = pd.to_datetime(self.dataset[datecol]).dt.date


    def loadDataRenamingCols(self,rcCol:str|None=None,datecol:str|None=None,ltCol:str|None=None,deltaLRVCol:str|None=None
                             ,lrvCol:str|None=None, ceilCol:str|None=None):
        renameDict={}
        if not rcCol is None:
            renameDict[self.rcacCol]=rcCol
        if not datecol is None:
            renameDict[self.dateCol] = datecol
        if not ltCol is None:
            renameDict[self.ltCol]=ltCol
        if not deltaLRVCol is None:
            renameDict[self.deltaLRVCol]=deltaLRVCol
        if not lrvCol is None:
            renameDict[self.lrvCol] = lrvCol
        if not ceilCol is None:
            renameDict[self.ceilCol] = ceilCol
        return self.dataset.rename(renameDict, axis=1)

    def getDeltaLRVObjectForCalib(self):
        # Sticking this in here incase  the file loader loads it specifically as the deltaLRV handler directly
        return self

    @staticmethod
    def loadAndProcessDeltaLRVFile(filepath,filereader,capdateCol:str='capDate',rcacCol:str='roomclass',ltCol:str='occLT',deltaLRVCol:str='deltaLRV',dateCol:str='occupancyDate',isCalib:bool=False):
        df = filereader(filepath)
        # fix the datatypes up
        df[dateCol] = pd.to_datetime(df[dateCol]).dt.date
        capDateColPresent = capdateCol in df.columns
        ltColPresent = ltCol in df.columns

        if not capDateColPresent and not ltColPresent:
            raise RuntimeError('deltaLRV file must have either a captureDate Column')
        elif ltColPresent and not capDateColPresent: # we need to generate the LT Col
            df[capdateCol] = df[[dateCol,ltCol]].apply(lambda x: x[dateCol]-timedelta(days=x[ltCol]),axis=1)
        elif not ltColPresent and capDateColPresent:
            df[capdateCol] = pd.to_datetime(df[capdateCol]).dt.date
            df[ltCol] = (pd.to_datetime(df[dateCol])-pd.to_datetime(df[capdateCol])).apply(lambda x:x.days)

        genericArgs = dict(capdateCol=capdateCol, rcacCol=rcacCol, ltCol=ltCol, dateCol=dateCol,deltaLRVCol=deltaLRVCol)
        if isCalib:
            datalib = {}
            for capdate, subdf in df.groupby(capdateCol):
                datalib[capdate] = GenericDeltaLRVHandler(dataset=subdf.reset_index(drop=True), **genericArgs)
            return datalib
        return GenericDeltaLRVHandler(dataset=df, **genericArgs)

    def subsetRC(self, allowedRCList, forceAllDefault:bool=False):
            if self.rcacCol in self.dataset.columns and not forceAllDefault:
                    self.dataset = self.dataset.query(f"{self.rcacCol} in @allowedRCList").reset_index(drop=True)
            else:
                replacementLRV = []
                for rc in allowedRCList:
                    self.dataset[self.rcacCol] = rc
                    replacementLRV.append(self.dataset.copy())
                self.dataset = pd.concat(replacementLRV, ignore_index=True)
