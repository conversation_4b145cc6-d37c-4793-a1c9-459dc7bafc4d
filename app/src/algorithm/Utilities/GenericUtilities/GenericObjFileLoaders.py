from datetime import date
from pathlib import Path
from typing import Dict, List, Callable

import pandas as pd

from src.algorithm.Utilities.GenericUtilities.GenericDeltaLRVHandler import GenericDeltaLRVHandler
from src.algorithm.Utilities.GenericUtilities.GenericDeltaOccHandler import GenericDeltaOccHandler
from src.algorithm.Utilities.GenericUtilities.GenericRefRateHandler import GenericRefRateHandler


class GenericDeltaLoader:
    def __init__(self):
        raise RuntimeError(f'{self.__class__} is not a loadable object')

    @staticmethod
    def __determineFileTypeReader__(filename:str)->Callable:
        filename = filename.split('.')[-1]
        if filename == 'json':
            reader = pd.read_json
        elif filename == 'csv':
            reader = pd.read_csv
        elif filename in ['pqt', 'parquet']:
            reader = pd.read_parquet
        elif filename in ['xls', 'xlsx']:
            reader = pd.read_excel
        elif filename == 'sql':
            reader = pd.read_sql
        elif filename == 'sas7bdat':
            reader = pd.read_sas
        elif filename in ['pkl', 'pickle']:
            reader = pd.read_pickle
        elif filename in ['xml']:
            reader = pd.read_xml
        else:
            raise RuntimeError(f'Unknown file extension {filename}')
        return reader

    @staticmethod
    def loadRefRateFile(filepath: Path
                        , filereader:Callable
                        , defaultRC: int
                        , rcacCol: str = 'roomclass'
                        , ltCol: str = 'occLT'
                        , refRateCol: str = 'refRate'
                        , dateCol: str = 'occupancyDate'
                        , capDateCol: str = 'captureDate'
                        , distinctRC: List[int]| None=None
                        ) -> Dict[int, Dict[date, GenericRefRateHandler]]:
        return GenericDeltaLoader.__dictify__(
            df=GenericDeltaLoader.__processRCFromInputFile__(filepath=filepath, filereader=filereader, rcacCol=rcacCol,
                                                             distinctRC=distinctRC, defaultRC=defaultRC)
            , classObj=GenericRefRateHandler
            , colargs=dict(rcacCol=rcacCol, capDateCol=capDateCol)
            , constantArgs=dict(rcacCol=rcacCol, ltCol=ltCol, refRateCol=refRateCol, dateCol=dateCol,
                                capDateCol=capDateCol)
            , variableArgs=dict(objID='dataset', capDate='capDate')
        )

    @staticmethod
    def loadDeltaOccFromDeltaOccFile(filepath: Path
                                     , filereader:Callable
                                     , defaultRC: int
                                     , rcacCol: str = 'roomclass'
                                     , ltCol: str = 'occLT'
                                     , deltaOccCol: str = 'deltaOcc'
                                     , dateCol: str = 'occupancyDate'
                                     , capDateCol: str = 'captureDate'
                                     , remainingCapacityCol:str = 'availableCapacity'
                                     , distinctRC: List[int]| None=None
                     ) -> Dict[int, Dict[date, GenericDeltaOccHandler]]:
        """
        This will read a delta Occ file
        :param filepath: pathlib.Path pointing ot the location of the file
        :param filereader: Function that reads the file and outputs a dataframe. Will call filereader(filepath)->pd.DataFrame
        :param defaultRC: Int that specifies the default RC to use if none are present. This will vary by system
        :param rcacCol: Column for roomclass/accomclass
        :param ltCol: Column for leadtime. Delta between occDate and CapDate
        :param deltaOccCol: column that has the change in occupancy over each leadtime
        :param dateCol: column for the occupancy Date
        :param capDateCol: Column for the captureDate
        :param distinctRC: If we have distinct RC to limit things by, this will be used to determine what RC are allowed.
        Only allow RC/AC present in the reference rates files.
        :return: {rcid:{capdate:deltaOccObj}}
        """

        return GenericDeltaLoader.__dictify__(
            df=GenericDeltaLoader.__processRCFromInputFile__(filepath=filepath, filereader=filereader, rcacCol=rcacCol, distinctRC=distinctRC, defaultRC=defaultRC)
            ,classObj=GenericDeltaOccHandler
            ,colargs=dict(rcacCol=rcacCol,capDateCol=capDateCol)
            ,constantArgs=dict(rcacCol=rcacCol,ltCol=ltCol,deltaOccCol=deltaOccCol,dateCol=dateCol,capDateCol=capDateCol,remainingCapacityCol=remainingCapacityCol)
            ,variableArgs=dict(objID='dataset',capDate='capDate')
        )

    @staticmethod
    def loadDeltaLRVFromDeltaLRVFile(filepath: Path, filereader
                                     , defaultRC: int
                                     , rcacCol: str = 'roomclass'
                                     , ltCol: str = 'occLT'
                                     , deltaLRVCol: str = 'deltaLRV'
                                     , dateCol: str = 'occupancyDate'
                                     , capDateCol: str = 'captureDate'
                                     , distinctRC: List[int] | None = None
                                     , ceilCol: str = 'Ceiling_Value'
                                     , lrvCol: str = 'LRV'
                     ) -> Dict[int, Dict[date, GenericDeltaLRVHandler]]:
        """

        :param filepath: pathlib.Path pointing ot the location of the file
        :param filereader: Function that reads the file and outputs a dataframe.r:
        :param defaultRC: Int that specifies the default RC to use if none are p:
        :param rcacCol: Column for roomclass/accomclass
        :param ltCol: Column for leadtime. Delta between occDate and CapDate
        :param deltaLRVCol:
        :param dateCol: column for the occupancy Date
        :param capDateCol: Column for the captureDate
        :param distinctRC: If we have distinct RC to limit things by, this will be used to determine what RC are allowed.
        Only allow RC/AC present in the deltaocc file.
        :return: {rcid:{capdate:deltaLRVObj}}
        """
        return GenericDeltaLoader.__dictify__(
            df=GenericDeltaLoader.__processRCFromInputFile__(filepath=filepath, filereader=filereader, rcacCol=rcacCol, distinctRC=distinctRC, defaultRC=defaultRC)
            ,classObj=GenericDeltaLRVHandler
            ,colargs=dict(rcacCol=rcacCol,capDateCol=capDateCol)
            ,constantArgs=dict(rcacCol=rcacCol,ltCol=ltCol,deltaLRVCol=deltaLRVCol,dateCol=dateCol,capDateCol=capDateCol,ceilCol=ceilCol,lrvCol=lrvCol)
            ,variableArgs=dict(objID='dataset',capDate='capDate')
        )


    @staticmethod
    def __processRCFromInputFile__(filepath:Path, filereader:Callable, rcacCol, distinctRC, defaultRC):
        # read input file. File reader will be something like pd.read_csv or pd.read_parquet....
        df = filereader(filepath)

        # see if there is an intersection between distinct RC and presentRC
        if not rcacCol in df.columns:
            # what do wedo if not RC Cols present
            if distinctRC is None:
                df[rcacCol] = defaultRC
            else:
                dflist = []
                for rcid in distinctRC:
                    df[rcacCol] = rcid
                    dflist.append(df)
                df = pd.concat(dflist)
        else:
            if not distinctRC is None:
                presentRC = df[rcacCol].unique().tolist()
                if not isinstance(distinctRC,list):
                    distinctRC = [distinctRC]
                intersection = [x for x in presentRC if x in distinctRC]
                if len(intersection) == 0:
                    raise RuntimeError(f"No Computable intersection between distinctRC and available RC is present")
                df = df.query(f"{rcacCol} in {intersection}").reset_index(drop=True)
            # if we don't have distinct RC then we just return df
        return df


    @staticmethod
    def __dictify__(df,classObj:Callable,colargs:dict,constantArgs:dict,variableArgs:Dict) -> \
            Dict[int, Dict[date, GenericDeltaOccHandler|GenericDeltaLRVHandler|GenericRefRateHandler]]:
        rcacCol=colargs['rcacCol']
        capDateCol = colargs['capDateCol']
        results={}
        for rcid in df[rcacCol].unique().tolist():
            results[rcid] = {}
        for (rcid, capdate), subdf in df.groupby([rcacCol, capDateCol]):
            varargs = {variableArgs['objID']:subdf,variableArgs['capDate']:capdate}
            inargs = {**constantArgs,**varargs}
            han = classObj(**inargs)
            results[rcid][capdate] = han
        return results
