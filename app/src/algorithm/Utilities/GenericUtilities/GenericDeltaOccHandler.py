from dataclasses import dataclass
from datetime import date, datetime
import pandas as pd
import numpy as np



"""
We are moving things to have generic handlers since there is a common pattern here.
The calibrator and evaluator can calculate these things, but abstracting this away gives us an advantage
over other approaches in that it allows us to instead load these on the fly. 
The advantage of this for speeding up evaluations should be massive. 
"""
@dataclass()
class GenericDeltaOccHandler:
    dataset:pd.DataFrame
    capDate:date|datetime|None=None  # this is maybe not important most ofthe time
    # columns now
    rcacCol:str='roomclass' # room class or accom class
    ltCol:str='occLT'
    deltaOccCol:str='deltaOcc'
    dateCol:str='occupancyDate'
    remainingCapacityCol:str='availableCapacity'
    capDateCol:str='captureDate'


    def __post_init__(self):
        self.__correctDateCols__()
        self.percChangeInRemainingCapacityCol = f"__percChange_{self.remainingCapacityCol}__"
        if not self.remainingCapacityCol in self.dataset.columns:
            self.dataset[self.remainingCapacityCol] = 0.
        self.dataset[self.remainingCapacityCol]=self.dataset[self.remainingCapacityCol].fillna(0.)
        self.dataset[self.percChangeInRemainingCapacityCol] = self.calcPercentageChangeInOccReltoRemainingCapacity(occChange=self.dataset[self.deltaOccCol]
                                                                                                              ,remCapacity=self.dataset[self.remainingCapacityCol])
        self.dataset = self.dataset[[
            self.rcacCol, self.dateCol,self.ltCol, self.deltaOccCol,
        self.capDateCol,self.remainingCapacityCol,self.percChangeInRemainingCapacityCol]].sort_values([self.rcacCol, self.dateCol,self.ltCol],ascending=[True,True,True],ignore_index=True)

    def __correctDateCols__(self):
        for datecol in [self.capDateCol,self.dateCol]:
            self.dataset[datecol] = pd.to_datetime(self.dataset[datecol]).dt.date



    def loadDataRenamingCols(self,rcCol:str|None=None,datecol:str|None=None,ltCol:str|None=None,deltaOccCol:str|None=None):
        renameDict={}
        if not rcCol is None:
            renameDict[self.rcacCol]=rcCol
        if not datecol is None:
            renameDict[self.dateCol] = datecol
        if not ltCol is None:
            renameDict[self.ltCol]=ltCol
        if not deltaOccCol is None:
            renameDict[self.deltaOccCol]=deltaOccCol
        return self.dataset.rename(renameDict, axis=1)

    def calcPercentageChangeInOccReltoRemainingCapacity(self,occChange: pd.Series, remCapacity: pd.Series):
        def calcfun(deltaOcc: float | np.float64, availCap: float | np.float64):
            # if capacity is negative or would become negative return 100% Those are already situation in which you would
            # want to run the calculation anyways
            if availCap <= 0 or deltaOcc >= availCap:
                if abs(deltaOcc)>0: # we don't want to allow a trigger if there are no solds.
                    return 1.
                else:
                    return 0.
            else:
                # otherwise an absolute persentage change in occupancy relative to the available remaining capacity is all
                # that is needed
                return abs(deltaOcc / availCap)
        return np.frompyfunc(calcfun, nin=2, nout=1)(occChange, remCapacity)


