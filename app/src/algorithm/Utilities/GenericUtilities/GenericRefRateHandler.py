from dataclasses import dataclass
from datetime import date, datetime
import pandas as pd

"""
We are moving things to have generic handlers since there is a common pattern here.
The calibrator and evaluator can calculate these things, but abstracting this away gives us an advantage
over other approaches in that it allows us to instead load these on the fly. 
The advantage of this for speeding up evaluations should be massive. 
"""


@dataclass()
class GenericRefRateHandler:
    dataset: pd.DataFrame
    capDate: date | datetime | None = None

    # columns now
    rcacCol: str = 'roomclass'  # room class or accom class
    ltCol: str = 'occLT'
    refRateCol: str = 'refRateCol'
    dateCol: str = 'occupancyDate'
    capDateCol:str = 'capdate'

    def __post_init__(self):
        '''From G3 the reference rate will be drawn from the bar fg  by arrivaldate and los=1 recast as occupancydate
        From Elevate, the reference rate will be the HRR value. The capturedate will have to be faked.
        '''

        self.__correctDateCols__()
        self.dataset = self.dataset[[
            self.rcacCol, self.dateCol, self.ltCol, self.refRateCol
        ]].sort_values([self.rcacCol, self.dateCol, self.ltCol], ascending=[True, True, True], ignore_index=True)

    def __correctDateCols__(self):
        for datecol in [self.capDateCol, self.dateCol]:
            self.dataset[datecol] = pd.to_datetime(self.dataset[datecol]).dt.date
    def loadDataRenamingCols(self, rcCol: str | None = None, datecol: str | None = None, ltCol: str | None = None,
                             refRateCol: str | None = None):
        renameDict = {}
        if not rcCol is None:
            renameDict[self.rcacCol] = rcCol
        if not datecol is None:
            renameDict[self.dateCol] = datecol
        if not ltCol is None:
            renameDict[self.ltCol] = ltCol
        if not refRateCol is None:
            renameDict[self.refRateCol] = refRateCol
        return self.dataset.rename(renameDict, axis=1)

