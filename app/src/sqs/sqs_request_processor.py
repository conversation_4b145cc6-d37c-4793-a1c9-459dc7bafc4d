import logging

import boto3

from src.common.base_classes.base_handler import <PERSON><PERSON><PERSON><PERSON>
from src.common.dto.sqs_message import SQSMessage
from src.common.enums.env_var import EnvironmentVariable
from src.common.env import get_value
from src.common.sqs_service import SQSService


class SQSRequestProcessor:

    def __init__(self, queue_url, msg_handler: BaseHandler,
                 sqs_service=SQSService(sqs_client=boto3.client('sqs',
                                                                endpoint_url=get_value(
                                                                    EnvironmentVariable.LOCALSTACK_URL, None)))):
        self.logger = logging.getLogger(__name__)
        self.sqs_service = sqs_service
        self.queue_url = queue_url
        self.msg_handler = msg_handler

    def process(self):
        messages: list[SQSMessage] = self.sqs_service.receive_message(self.queue_url)
        for message in messages:
            try:
                self.msg_handler.handle_request(message.body.event_source)
                self.sqs_service.delete_message(self.queue_url, message)
            except Exception as e:
                self.logger.exception(f"Failed to process message:{message} with exception:{e}")
