import logging
from datetime import datetime
from typing import override

from src.propertyMap import propMap
from src.common.base_classes.base_handler import BaseHand<PERSON>
from src.common.calibration_service import CalibrationService
from src.common.decorators.property_context_dec import set_context_from_args
from src.common.dto.DecisionAnalysisReports import DecisionAnalysisReports
from src.common.dto.GenerateAssemblageMrRequestSource import GenerateAssemblageMrRequestSource
from src.common.dto.calib_request import CalibrationRequestSource
from src.common.dto.revise_potential_request import RevisePotentialRequestSource
from src.common.env import S3_DATA_BUCKET_NAME
from src.common.reports_service import reports_service
from src.common.s3_service import s3_service


class CalibrationRequestHandler(BaseHandler):

    def __init__(self, calibration_service: CalibrationService):
        self.calibration_service = calibration_service
        self.logger = logging.getLogger(__name__)

    @override
    def handle_request(self, request: CalibrationRequestSource | GenerateAssemblageMrRequestSource | RevisePotentialRequestSource | DecisionAnalysisReports):
        self.logger.info(f'Processing {request}')
        if isinstance(request, CalibrationRequestSource):
            self.handle_calibration_request(request)
        elif isinstance(request, GenerateAssemblageMrRequestSource):
            self.handle_mr_request(request)
            
        elif isinstance(request, RevisePotentialRequestSource):
            self.handle_revision_req(request)
      
        elif isinstance(request, DecisionAnalysisReports):
            self.handle_decision_analysis_reports(request)

    @set_context_from_args
    def handle_calibration_request(self, request: CalibrationRequestSource):
        (self.calibration_service
         .run_full_calibration_from_s3_save_results(source_request=request))

    @set_context_from_args
    def handle_mr_request(self, request: GenerateAssemblageMrRequestSource):
        self.logger.debug(f"Processing MR Request {request}")
        report = reports_service.generate_report(request.client_code, request.property_code, request.g3Url,
                                                 request.caught_up_dates, request.property_id)
        s3_service.save_file(report, rf's3://{S3_DATA_BUCKET_NAME}/{request.client_code}/{request.property_code}/reports/{request.report_id}.xlsx')
        self.logger.debug(f"Done processing report {request.report_id}")

    @set_context_from_args
    def handle_revision_req(self, request: RevisePotentialRequestSource):
        self.logger.debug(f"Processing Revision {request}")
        self.calibration_service.run_threshold_revision_v2(request.client_code, request.property_code)
        self.logger.debug(f"Processing Revision {request}")


    @set_context_from_args
    def handle_decision_analysis_reports(self, request: DecisionAnalysisReports):
        report = reports_service.fetch_reports(request)
        with open(f"C:/Users/<USER>/Downloads/decision_analysis_reports_{datetime.now().date()}.xlsx", 'wb') as f:
            f.write(report.getvalue())
        # s3_service.save_file(report, rf's3://{S3_DATA_BUCKET_NAME}/reports/decision_analysis_reports_{datetime.now().date()}.xlsx')

if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)

    request_handler = CalibrationRequestHandler(CalibrationService())

    # input_property_codes = ['SAVPO','LONEA','CLTAC','GRRND','OLBCL','CGHEM','HSNZH','LLFGI','YQPUK',
    #                         'RDUHS','ALMHH','ANBOX','AVPWI','ABISW','ABITX','ABQMT','ABQRR','ABRSD',
    #                         'ACYVI','ADDIL','ADEGA','AGOCA','AEXLA','AMADU','AIKGI','AMEGA','AHNGA',
    #                         'ALBHS','AHNMD','ALWWA','ALBJC','ALBSP','ANDNO','APFFL','APLHH','ARKAR',
    #                         'ARTNY','ASBNC','ASHNH','ATHTN','ATLSM','ATLCP','ATLTC','ATLWS','ATOOH',
    #                         'ATLCY','ATWAK','ATLDL','ATLDM','AUBAL','ATLDV','AUOLN','CITTK','SAVHV',
    #                         'BOSCO','CHSHD','LAXAG','MSPHW','SAVHS','ATLCL','MEMSG','SLBJA','TLHTL',
    #                         'BFLBA','BCTFL','TYSAP','SFOEM','ARBVW','AUOAP','ALDVA','ALBSS','ALBSY',
    #                         'AIKSC','ALBAH','ABQAA','LITDT','ORDCH','MLUWM','SAVNO','SAVAH','SANBE',
    #                         'SFOAN','SMFRH','SNAIR','TKATX','DALLE','GNVGF','AMWGI','ANBHS','ATLFB',
    #                         'AUOOP','ATLFY','AUSAN','ATLGT','ATLHM','ATLKE','ATLLV','ATLNB','ATLNL',
    #                         'ATLNO','ATLSB','ATLSC','ATLSL','AUSAP','LAPLA','TPANH','ACVCA','CHIHW',
    #                         'CAKSO','STRPK','BRSBC','SAVMN','OKCAI','CSXZH','DXBCD','REKCU','ABIDT',
    #                         'PHLMV','EWRNA','CKZCA','MAFID','LONME','ZRHHI','BUEHI','EDICC','AQGSS',
    #                         'ABIHS','CHSAS','LAXTH','ADBMA','CUNQR','FLLIP','BOSBR','SSHSB','ORYCL']

    input_property_codes = ['ANRHI','ATLCD','BTRCA','BWIHP','CMHDW',
                            'CMIUR','DALDX','DCALW','DERDT','DETNO',
                            'DOHHD','DSMDN','DTWTM','FLLCI','GDLGM',
                            'GSPCP','GSPRU','MANDG','MIAAA','MSPNB',
                            'MYRBH','ORDEV','PERHI','PVRPA','QDHAS',
                            'QFBHX','SFORF','SHEYA','STLFH','TIJTO']

    input_client_codes = ['Hilton']

    report = DecisionAnalysisReports(
        g3Url="https://g3.ideas.com",
        client_codes_to_include=input_client_codes * len(input_property_codes),
        property_codes_to_include=input_property_codes,
        report_id="test"
    )

    logging.info(f"Starting to generate the Decision Analysis Reports...{datetime.now()}")
    request_handler.handle_request(report)
    logging.info(f"Report generation completed successfully.{datetime.now()}")
