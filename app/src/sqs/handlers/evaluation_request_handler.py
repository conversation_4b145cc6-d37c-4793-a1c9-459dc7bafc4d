import logging
from typing import override

from src.common.base_classes.base_handler import <PERSON><PERSON><PERSON><PERSON>
from src.common.decorators.property_context_dec import set_context_from_args
from src.common.dto.evaluate_request import EvaluationRequestSource
from src.common.evaluation_service import EvaluationService


class EvaluationRequestHandler(BaseHandler):

    def __init__(self, evaluation_service: EvaluationService):
        self.evaluation_service = evaluation_service
        self.logger = logging.getLogger(__name__)

    @override
    @set_context_from_args
    def handle_request(self, request: EvaluationRequestSource):
        self.logger.info(f'Processing {request}')
        (self.evaluation_service
         .evaluate(evaluation_req_source=request))


