from src.common.calibration_service import CalibrationService
from src.common.enums.env_var import EnvironmentVariable
from src.common.env import get_value
from src.common.evaluation_service import EvaluationService
from src.sqs.handlers.calib_request_handler import Calibration<PERSON><PERSON><PERSON>Handler
from src.sqs.handlers.evaluation_request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>andler
from src.sqs.sqs_request_listener import SQSRequestListener


class ListenersFactory:

    @staticmethod
    def calibration_listener() -> SQSRequestListener:
        return SQSRequestListener.from_queue_url_and_handler(get_value(EnvironmentVariable.CALIB_REQ_URL, ""),
                                                             request_handler=CalibrationRequestHandler(
                                                                 CalibrationService()))

    @staticmethod
    def evaluation_listener() -> SQSRequestListener:
        return SQSRequestListener.from_queue_url_and_handler(get_value(EnvironmentVariable.EVALUATION_REQ_URL, ""),
                                                             request_handler=EvaluationRequestHandler(
                                                                 EvaluationService()))
