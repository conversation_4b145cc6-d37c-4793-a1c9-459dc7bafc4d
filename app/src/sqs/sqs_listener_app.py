from src.sqs.listenersfactory import ListenersFactory


class SQSListenerApp:

    def __init__(self, listeners: ListenersFactory):
        self.__calibration_listener = listeners.calibration_listener()
        self.__evaluation_listener = listeners.evaluation_listener()

    def start(self):
        self.start_calibration_listener()
        self.start_evaluation_listener()

    def stop(self):
        self.stop_calibration_listener()
        self.stop_evaluation_listener()

    def start_calibration_listener(self):
        self.__calibration_listener.start()

    def stop_calibration_listener(self):
        self.__calibration_listener.stop()
        self.__calibration_listener.join()

    def start_evaluation_listener(self):
        self.__evaluation_listener.start()

    def stop_evaluation_listener(self):
        self.__evaluation_listener.stop()
        self.__evaluation_listener.join()

if __name__ == '__main__':
    sqs_app = SQSListenerApp(listeners=ListenersFactory())
    sqs_app.start()
