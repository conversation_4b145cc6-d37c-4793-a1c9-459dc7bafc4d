import logging

from src.log_config.setup import setup_logger

setup_logger()
from src.sqs.listenersfactory import ListenersFactory
from src.common.enums.env_var import EnvironmentVariable
from src.common.env import get_value

from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI
from src.controller.routers import calibrate, report, sqs_listener
from src.controller.routers import evaluation
from src.controller.routers import health_check
from src.controller.routers import log_management


@asynccontextmanager
async def lifespan(app: FastAPI):
    default_thread_count = 5
    listeners = ListenersFactory()
    num_calib_threads = int(get_value(EnvironmentVariable.CALIB_THREAD_COUNT, default_thread_count))
    num_eval_threads = int(get_value(EnvironmentVariable.EVALUATION_THREAD_COUNT, default_thread_count))
    app.state.calib_app = [listeners.calibration_listener() for _ in range(num_calib_threads)]
    app.state.eval_app =  [listeners.evaluation_listener() for _ in range(num_eval_threads)]

    for listeners in [app.state.calib_app, app.state.eval_app]:
        for i, listener in enumerate(listeners, start=1):
            listener.start()

    yield

    for listeners in [app.state.calib_app, app.state.eval_app]:
        for i, listener in enumerate(listeners, start=1):
            listener.stop()
            listener.join()


app = FastAPI(lifespan=lifespan)

app.include_router(calibrate.router)
app.include_router(evaluation.router)
app.include_router(health_check.router)
app.include_router(log_management.router)
app.include_router(report.router)
app.include_router(sqs_listener.router)

if __name__ == '__main__':
    setup_logger()
    uvicorn.run(app="__main__:app", host="localhost", port=8000, reload=True, workers=1)
