import logging
from datetime import datetime
from typing import Any
from uuid import uuid4

from fastapi import APIRouter
from starlette.responses import StreamingResponse

from src.common.dto.DecisionAnalysisReports import DecisionAnalysisReports
from src.common.dto.GenerateAssemblageMrRequestSource import GenerateAssemblageMrRequestSource
from src.common.env import ENV
from src.common.reports_service import reports_service
from src.common.sns_service import SNSService

router = APIRouter(prefix="/report", tags=['MonitoringReports'])

logger = logging.getLogger(__name__)

@router.get('/assemblage-report')
def generate_assemblage_report(req: GenerateAssemblageMrRequestSource):
    try:
        output = reports_service.generate_report(req.client_code, req.property_code, req.g3Url, req.caught_up_dates,
                                                 req.property_id)

        response = StreamingResponse(
            output,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )
        # Add a content-disposition header to suggest a file name
        response.headers["Content-Disposition"] = "attachment; filename=report.xlsx"
        return response
    except Exception as e:
        logger.exception(f"failed to process request {req} {e}")
    return "failed to process request"

@router.post('/async/assemblage-report')
def trigger_assemblage_report(req: GenerateAssemblageMrRequestSource):
    try:
        report_id = uuid4()
        event = {
            "sourceSystem": "dyn-opt",
            "eventType": "GENERATE_ASSEMBLAGE_MR",
            "scope": ENV,
            "version": "1",
            "eventDateTime": datetime.now().isoformat(),
            "eventSource": {
                "clientCode": req.client_code,
                "propertyCode": req.property_code,
                "propertyId": req.property_id,
                "g3Url": req.g3Url,
                "caughtUpDates": [d.isoformat() for d in req.caught_up_dates],
                "reportId": str(report_id)
            }
        }
        SNSService().send_raw(event)
        return str(report_id)
    except Exception as e:
        logger.exception(f"Exception occurred: {e}")
    return "Failed Check Logs"

@router.post('/event-sns')
def send_sns_event(event: dict[str, Any]) -> str:
    try:
        SNSService().send_raw(event)
    except Exception as e:
        logger.exception(f"Failed to send SNS event {event} {e}")
    return "Event Sent"

@router.post('/async/decision-analysis-reports')
def trigger_decision_analysis_reports(req: DecisionAnalysisReports):
    try:
        report_id = uuid4()
        event = {
            "sourceSystem": "dyn-opt",
            "eventType": "GENERATE_DECISION_ANALYSIS_REPORTS",
            "scope": ENV,
            "version": "1",
            "eventDateTime": datetime.now().isoformat(),
            "eventSource": {
                "clientCodesToInclude": req.client_codes_to_include,
                "propertyCodesToInclude": req.property_codes_to_include,
                "propertyIdsToInclude": req.property_ids_to_include,
                "g3Url": req.g3Url,
                "reportId": str(report_id)
            }
        }
        SNSService().send_raw(event)
        return str(report_id)
    except Exception as e:
        logger.exception(f"Exception occured: {e}")
    return "Failed Check Logs"
