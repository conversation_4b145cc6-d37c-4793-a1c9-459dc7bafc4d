import logging

from fastapi import APIRouter

from src.common.dto.evaluation_local_file_input import EvaluationLocalFileInput
from src.common.dto.evaluation_result import EvaluationResult
from src.common.dto.evaluation_s3_file_input import EvaluationS3FileInput
from src.common.evaluation_service import EvaluationService

router = APIRouter(prefix="/evaluate", tags=['evaluation'])
log = logging.getLogger(__name__)


@router.post("/using-s3-files")
def evaluate_using_s3_files(s3_path_holder: EvaluationS3FileInput) -> EvaluationResult:
    return EvaluationService().run_evaluate_using_s3_files(s3_path_holder)


@router.post("/using-local-files")
def evaluate_using_local_files(local_path_holder: EvaluationLocalFileInput) -> EvaluationResult:
    return EvaluationService().run_evaluate_using_local_files(local_path_holder)
