import logging.config
import logging
from enum import Enum
from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel
from fastapi import APIRouter


router = APIRouter(prefix="/log-management", tags=['LogManagement'])


class LogLevel(Enum):
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARN = logging.WARN
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


class ChangeLogLevelRequestBody(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True, use_enum_values=False)

    log_level: LogLevel
    module_name: str


@router.post('/set-log-level')
def set_log_level(log_level_request: ChangeLogLevelRequestBody):
    logging.getLogger(log_level_request.module_name).setLevel(log_level_request.log_level.value)
    return {'message': f'Updated Log level to {str(log_level_request.log_level.name)}'}
