import logging

from fastapi import APIRouter
from fastapi import Request
import boto3
import requests

from src.common.enums.env_var import EnvironmentVariable
from src.common.env import get_value
from src.sqs.listenersfactory import ListenersFactory

log = logging.getLogger(__name__)
ecs_client = boto3.client('ecs', region_name=get_value(EnvironmentVariable.AWS_REGION, ''))

def get_ecs_instance_ips():
    """Fetch all running ECS instance IPs and log any errors."""
    try:
        task_arns = ecs_client.list_tasks(cluster=get_value(EnvironmentVariable.ECS_CLUSTER_NAME, ''), serviceName=get_value(EnvironmentVariable.ECS_SERVICE_NAME, '')).get("taskArns", [])
        if not task_arns:
            log.info("No running ECS tasks found.")
            return []

        tasks = ecs_client.describe_tasks(cluster=get_value(EnvironmentVariable.ECS_CLUSTER_NAME, ''), tasks=task_arns).get("tasks", [])
        if not tasks:
            log.info("No task details found.")

        instance_ips = {
            container["networkInterfaces"][0]["privateIpv4Address"]
            for task in tasks
            for container in task.get("containers", [])
            if "networkInterfaces" in container and container["networkInterfaces"]
        }

        return list(instance_ips)

    except boto3.exceptions.Boto3Error as e:
        log.error(f"AWS Boto3 error: {e}", exc_info=True)
    except Exception as e:
        log.error(f"Unexpected error while fetching ECS instance IPs: {e}", exc_info=True)

    return []

def execute_API(ip_address: str, port: str, prefix: str, api_endpoint: str):
    url = f"http://{ip_address}:{port}{prefix}{api_endpoint}"
    try:
        log.info(f'Executing {api_endpoint} ... ')
        if api_endpoint == '/health-check':
            response = requests.get(url, timeout=5)
        else:
            response = requests.post(url, timeout=5)
        log.info(f'Successfully executed {api_endpoint} for {ip_address}: {response.status_code} - {response.text}')
        return response.content
    except requests.RequestException as e:
        log.error(f'Failed to execute {api_endpoint} for {ip_address}: {e}')

def stop_thread(i, c):
    if c.is_alive():
        log.info(f"Stopping Calibration Thread {i}...")
        c.stop()
        c.join()
        return i
    else:
        log.info(f"Calibration Thread {i} already stopped.")
        return None

router = APIRouter(prefix="/sqs-listeners", tags=['sqs-listeners'])

@router.post('/{status}/{process}')
def change_process_status(status: str, process: str):
    log.info('Changing process status...')
    ecs_instance_ips = get_ecs_instance_ips()
    result = {}
    for ip in ecs_instance_ips:
        content = execute_API(ip, '8000', router.prefix, f'/{status}-{process}')
        result[ip] = content
    log.info('Successfully changed process status.')
    return result


@router.post('/stop-calibration')
def stop_calibration(request: Request):
    calib_apps = request.app.state.calib_app
    stopped = [res for i, c in enumerate(calib_apps, start=1)
               if (res := stop_thread(i, c))]
    log.info(f"Successfully stopped calibration threads: {stopped}")

@router.post('/start-calibration')
def start_calibration(request: Request):
    calib_apps = request.app.state.calib_app
    alive_threads = [i for i, c in enumerate(calib_apps, 1) if c.is_alive()]

    if alive_threads:
        log.info(f'Calibration threads already running: {alive_threads}')
        return

    log.info('Starting all calibration threads...')
    listeners = ListenersFactory()
    num_calib_threads = len(calib_apps)
    request.app.state.calib_app = [
        listeners.calibration_listener() for _ in range(num_calib_threads)
    ]
    list(
        map(
            lambda ic: (
                ic[1].start(),
                log.info(f'Started Calibration Thread {ic[0]}.')
            ),
            enumerate(request.app.state.calib_app, start=1)
        )
    )

    log.info('All calibration threads started.')


@router.post('/stop-evaluation')
def stop_evaluation(request: Request):
    eval_apps = request.app.state.eval_app
    stopped = [
        res for i, e in enumerate(eval_apps, start=1)
        if (res := stop_thread(i, e))
    ]
    log.info(f"Successfully stopped evaluation threads: {stopped}")
    return {"stopped_threads": stopped}


@router.post('/start-evaluation')
def start_evaluation(request: Request):
    eval_apps = request.app.state.eval_app
    alive_threads = [i for i, e in enumerate(eval_apps, start=1) if e.is_alive()]

    if alive_threads:
        log.info(f'Evaluation threads already running: {alive_threads}')
        return {"already_running": alive_threads}

    listeners = ListenersFactory()
    num_eval_threads = len(eval_apps)
    request.app.state.eval_app = [
        listeners.evaluation_listener() for _ in range(num_eval_threads)
    ]
    list(
        map(
            lambda ie: (
                ie[1].start(),
                log.info(f'Started Evaluation Thread {ie[0]}.')
            ),
            enumerate(request.app.state.eval_app, start=1)
        )
    )

    return {"started_threads": list(range(1, num_eval_threads + 1))}

@router.post('/stop-all')
def stop_all(request: Request):
    stop_calibration(request)
    stop_evaluation(request)

@router.post('/start-all')
def start_all(request: Request):
    start_calibration(request)
    start_evaluation(request)

@router.get('/health-check')
def health_check(request: Request):
    calib_apps = request.app.state.calib_app
    eval_apps = request.app.state.eval_app
    return {
        'calibration': f'{sum(c.is_alive() for c in calib_apps)} / {len(calib_apps)} running',
        'evaluation': f'{sum(e.is_alive() for e in eval_apps)} / {len(eval_apps)} running'
    }