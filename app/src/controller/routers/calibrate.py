import logging

from fastapi import APIRouter

from src.common.calibration_service import CalibrationService
from src.common.dto.calibrated_potential import CalibratedPotential
from src.common.dto.calibration_local_file_input import CalibrationLocalFileInput
from src.common.dto.calibration_s3_file_input import CalibrationS3FileInput

router = APIRouter(prefix="/calibrate", tags=['calibrate'])
log = logging.getLogger(__name__)


@router.post("/using-s3-files")
def calibrate_using_s3_files(s3_path_holder: CalibrationS3FileInput) -> list[CalibratedPotential]:
    return CalibrationService().run_calibration_from_s3(s3_path_holder)


@router.post("/using-local-files")
def calibrate_using_local_files(local_path_holder: CalibrationLocalFileInput) -> list[CalibratedPotential]:
    return CalibrationService().run_calibration_from_local_files(local_path_holder)
