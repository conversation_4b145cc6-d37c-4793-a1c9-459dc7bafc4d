import os
from unittest.mock import patch, MagicMock, call

from src.common.dto.calibrated_potential import CalibratedPotential
from src.common.dto.calibration_local_file_input import CalibrationLocalFileInput
from src.common.dto.calibration_s3_file_input import CalibrationS3FileInput
from src.controller.routers.calibrate import calibrate_using_s3_files, calibrate_using_local_files


@patch('src.common.calibration_service.CalibrationService.run_calibration_from_s3')
@patch('src.common.s3_service.S3Service.fetch_file')
def test_calibrate_using_s3_files(s3_fetch_file_mock: MagicMock, run_calibration_mock: MagicMock) -> None:
    calibrated_potentials = [CalibratedPotential(accom_class_id=1, calibrated_potential=10.01)]
    run_calibration_mock.return_value = calibrated_potentials
    s3_mock_path = 's3://mock'
    s3_path_holder = CalibrationS3FileInput(delta_occ_solds=s3_mock_path, reference_rate=s3_mock_path, delta_lrv=s3_mock_path, min_data_requirement=1, max_data_limit=1)
    actual = calibrate_using_s3_files(s3_path_holder)

    assert calibrated_potentials == actual
    assert run_calibration_mock.call_args == call(s3_path_holder)


@patch('src.common.calibration_service.CalibrationService.run_calibration_from_local_files')
def test_calibrate_using_local_s3_files(run_calibration_mock: MagicMock) -> None:
    calibrated_potentials = [CalibratedPotential(accom_class_id=1, calibrated_potential=10.01)]
    run_calibration_mock.return_value = calibrated_potentials
    local_mock_file = os.path.abspath(__file__)
    local_path_holder = CalibrationLocalFileInput(delta_occ_solds=local_mock_file, reference_rate=local_mock_file, delta_lrv=local_mock_file, min_data_requirement=1, max_data_limit=1)
    actual = calibrate_using_local_files(local_path_holder)

    assert calibrated_potentials == actual

    assert run_calibration_mock.call_args == call(local_path_holder)
