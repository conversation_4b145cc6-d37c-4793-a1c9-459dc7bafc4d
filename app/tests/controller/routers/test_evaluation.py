import datetime
import os
from pathlib import Path
from unittest.mock import patch, MagicMock, call

from src.common.dto.calibrated_potential import CalibratedPotential
from src.common.dto.evaluation_local_file_input import EvaluationLocalFileInput
from src.common.dto.evaluation_result import EvaluationResult
from src.common.dto.evaluation_s3_file_input import EvaluationS3FileInput

from src.controller.routers.evaluation import evaluate_using_s3_files, evaluate_using_local_files


@patch('src.common.evaluation_service.EvaluationService.run_evaluate_using_s3_files')
@patch('src.common.s3_service.S3Service.fetch_file')
def test_evaluation_using_s3_files(s3_fetch_file_mock: MagicMock, run_evaluation_mock: MagicMock) -> None:
    evaluation_result = [EvaluationResult(shouldOptimize=True, max_occupancy_date=datetime.date(2024, 1, 1), rc_to_occupancy_date_mapping={1:datetime.date(2025,1,1)})]
    calibrated_potentials = [CalibratedPotential(accom_class_id=1, calibrated_potential=10.01)]
    run_evaluation_mock.return_value = evaluation_result
    s3_mock_path = 's3://mock'
    s3_path_holder = EvaluationS3FileInput(delta_occ_solds=s3_mock_path, reference_rate=s3_mock_path, delta_lrv=s3_mock_path,
                                           min_data_requirement=1, max_data_limit=1, calibrated_potential=calibrated_potentials)
    actual = evaluate_using_s3_files(s3_path_holder)

    assert evaluation_result == actual
    assert run_evaluation_mock.call_args == call(s3_path_holder)


@patch('src.common.evaluation_service.EvaluationService.run_evaluate_using_local_files')
def test_evaluation_using_local_files_and_local_potential_file(run_evaluation_mock: MagicMock) -> None:
    evaluation_result = [EvaluationResult(shouldOptimize=False, max_occupancy_date=None, rc_to_occupancy_date_mapping={1:datetime.date(2025,1,1)})]
    run_evaluation_mock.return_value = evaluation_result
    local_mock_file = os.path.abspath(__file__)
    path = Path(local_mock_file)
    local_path_holder = EvaluationLocalFileInput(delta_occ_solds=local_mock_file, reference_rate=local_mock_file, delta_lrv=local_mock_file,
                                           min_data_requirement=1, max_data_limit=1, calibrated_potential=path)
    actual = evaluate_using_local_files(local_path_holder)

    assert evaluation_result == actual
    assert run_evaluation_mock.call_args == call(local_path_holder)

