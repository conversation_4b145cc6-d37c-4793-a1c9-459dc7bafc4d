import datetime
from http import HTT<PERSON>tatus

import os.path
from unittest.mock import MagicMock

import src.controller.routers.calibrate
from src.controller.api import app
from fastapi.testclient import TestClient

from src.common.dto.calibrated_potential import CalibratedPotential
from src.common.dto.evaluation_result import EvaluationResult
import src
test_app = TestClient(app)


def test_calibrate_api_using_local_files() -> None:
    path = os.path.abspath(__file__)
    local_calibrator_mock = MagicMock()
    src.common.calibration_service.CalibrationService.run_calibration_from_local_files = local_calibrator_mock
    req_body = {'deltaLrv': path, 'referenceRate': path, 'deltaOccSolds': path, 'minDataRequirement': 1,
                'maxDataLimit': 1}
    expected_potential = [CalibratedPotential(accom_class_id=1, calibrated_potential=10.1)]
    local_calibrator_mock.return_value = expected_potential
    potential = test_app.post('/calibrate/using-local-files', json=req_body)

    assert potential.status_code == HTTPStatus.OK
    assert [{'accomClassId': 1, 'calibratedPotential': 10.1}] == potential.json()


def test_calibrate_api_using_s3_files() -> None:
    path = 's3://mock'
    s3_calibrator_mock = MagicMock()
    src.common.calibration_service.CalibrationService.run_calibration_from_s3 = s3_calibrator_mock
    req_body = {'deltaLrv': path, 'referenceRate': path, 'deltaOccSolds': path, 'minDataRequirement': 1,
                'maxDataLimit': 1}
    expected_potential = [CalibratedPotential(accom_class_id=1, calibrated_potential=10.1)]
    s3_calibrator_mock.return_value = expected_potential
    potential = test_app.post('/calibrate/using-s3-files', json=req_body)

    assert potential.status_code == HTTPStatus.OK
    assert [{'accomClassId': 1, 'calibratedPotential': 10.1}] == potential.json()


def test_evaluate_api_using_local_files_when_should_optimize_is_true() -> None:
    path = os.path.abspath(__file__)
    local_evaluator_mock = MagicMock()
    src.common.evaluation_service.EvaluationService.run_evaluate_using_local_files = local_evaluator_mock
    req_body = {'deltaLrv': path, 'referenceRate': path, 'deltaOccSolds': path, 'minDataRequirement': 1,
                'maxDataLimit': 1, 'calibratedPotential': [
            {
                "accomClassId": 1,
                "calibratedPotential": 8
            }
        ]}
    local_evaluator_mock.return_value = EvaluationResult(should_optimize=True,
                                                         max_occupancy_date= datetime.date(2023, 1, 1),
                                                         rc_to_occupancy_date_mapping={2:datetime.date(2023, 1, 1)})
    potential = test_app.post('/evaluate/using-local-files', json=req_body)

    assert potential.status_code == HTTPStatus.OK
    assert {'shouldOptimize': True, 'maxOccupancyDate': '2023-01-01', 'rcToOccupancyDateMapping': {'2': '2023-01-01'}} == potential.json()


def test_evaluate_api_using_local_files_when_should_optimize_is_false() -> None:
    path = os.path.abspath(__file__)
    local_evaluator_mock = MagicMock()
    src.common.evaluation_service.EvaluationService.run_evaluate_using_local_files = local_evaluator_mock
    req_body = {'deltaLrv': path, 'referenceRate': path, 'deltaOccSolds': path, 'minDataRequirement': 1,
                'maxDataLimit': 1, 'calibratedPotential': [
            {
                "accomClassId": 1,
                "calibratedPotential": 8
            }
        ]}
    local_evaluator_mock.return_value = EvaluationResult(should_optimize=False, max_occupancy_date=None, rc_to_occupancy_date_mapping={})
    potential = test_app.post('/evaluate/using-local-files', json=req_body)

    assert potential.status_code == HTTPStatus.OK
    assert {'shouldOptimize': False, 'maxOccupancyDate': None, 'rcToOccupancyDateMapping': {}} == potential.json()
