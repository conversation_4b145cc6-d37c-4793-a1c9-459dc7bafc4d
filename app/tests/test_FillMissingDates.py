from datetime import datetime, timedelta

import pandas as pd
from pandas._testing import assert_frame_equal

from src.algorithm.Utilities.GenericUtilities.FillMissingDates import FillMissingDates

class TestFillMissingDates:

    def test_fill_missing_dates(self):

        TODAY = datetime(2025, 3, 24)

        fill_missing_dates_service = FillMissingDates()

        input_df = pd.DataFrame({
            'dates': [TODAY - timedelta(days=4), TODAY - timedelta(days=2), TODAY, TODAY + timedelta(days=1)],
            'values': [10, 20, 30, 40]
        })

        expected_df = pd.DataFrame({
            'dates': [
                TODAY - timedelta(days=13),
                TODAY - timedelta(days=12),
                TODAY - timedelta(days=11),
                TODAY - timedelta(days=10),
                TODAY - timedelta(days=9),
                TODAY - timedelta(days=8),
                TODAY - timedelta(days=7),
                TODAY - timedelta(days=6),
                TODAY - timedelta(days=5),
                TODAY - timedelta(days=4),
                TODAY - timedelta(days=3),
                TODAY - timedelta(days=2),
                TODAY - timedelta(days=1),
                TODAY,
                TODAY + timedelta(days=1)
            ],
            'values': [0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 20, 0, 30, 40]
        })

        result_df = fill_missing_dates_service.fill_missing_dates(df=input_df, date_column='dates')

        assert_frame_equal(result_df, expected_df)


