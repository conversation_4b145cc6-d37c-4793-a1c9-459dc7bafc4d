# Builder image used to build virtual env
FROM python:3.12.6-bullseye as builder

RUN pip install poetry==1.7.1

ENV POETRY_NO_INTERACTION=1 \
    POETRY_VIRTUALENVS_IN_PROJECT=1 \
    POETRY_VIRTUALENVS_CREATE=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

WORKDIR ../../app

COPY ../../app/pyproject.toml ../../app/poetry.lock ./
RUN touch README.md

RUN poetry install --only main --no-root && rm -rf $POETRY_CACHE_DIR

# The runtime image, used to just run the code provided its virtual environment
FROM python:3.12.6-slim-bullseye as runtime

ENV VIRTUAL_ENV=/app/.venv \
    PATH="../../app/.venv/bin:$PATH"

RUN apt-get -y update
RUN apt-get -y install curl

COPY --from=builder ${VIRTUAL_ENV} ${VIRTUAL_ENV}

COPY ../../app/src ../../app/src
COPY ../../app/run.bash ../../app/run.bash

WORKDIR ../../app
EXPOSE 8000

ENTRYPOINT /bin/bash run.bash
