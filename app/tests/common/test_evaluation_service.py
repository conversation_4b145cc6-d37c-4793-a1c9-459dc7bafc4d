from datetime import datetime
from pathlib import Path
from unittest.mock import MagicMock

import pandas as pd

from src.algorithm.evaluation_service import run_evaluation
from src.common.dto.evaluation_result import EvaluationResult
from src.common.ups_service import ups_service
from src.common.utilities.DateUtil import DateUtil


class TestEvaluationService:
    _window_size = 21
    _calib_values =  {6: -4.966037538925604, 7: -4.723261795759386, 8: -3.9029703894047807, 9: -4.4063318188412754}
    _use_leading_window = False
    _fail_on_missing_occ_dates = True
    _pressure_floor = 0.001
    _minHeuristicOccPercChangeThreshold = 0
    _persist_eval_op_at_rc_lvl = False
    _timezone = 'Asia/Dubai'
    _current_date = '2025-02-09'

    def test_run_evaluation(self):

        delta_occ_solds_path = Path(
            __file__).parent.parent / 'algorithm' / 'DynamicOptimizationPackage' / 'data' / 'TEST_delta_solds.csv'

        avail_cap_path = Path(
            __file__).parent.parent / 'algorithm' / 'DynamicOptimizationPackage' / 'data' / 'TEST_avail_cap.csv'

        delta_lrv_path = Path(
            __file__).parent.parent / 'algorithm' / 'DynamicOptimizationPackage' / 'data' / 'TEST_delta_lrv.csv'

        ref_price_path = Path(
            __file__).parent.parent / 'algorithm' / 'DynamicOptimizationPackage' / 'data' / 'TEST_ref_price.csv'

        get_property_timezone_mock = MagicMock()
        ups_service.get_param = get_property_timezone_mock
        get_property_timezone_mock.return_value = self._timezone

        get_current_date_for_timezone_mock = MagicMock()
        DateUtil.get_current_date_for_timezone = get_current_date_for_timezone_mock
        get_current_date_for_timezone_mock.return_value = self._current_date

        expected_result = EvaluationResult(should_optimize=True, max_occupancy_date=datetime(2025,3,4).date(), rc_to_occupancy_date_mapping={})

        expected_capture_date = '2025-02-09'

        result, cap_date, debug_frame = run_evaluation(
            file_reader=pd.read_csv,
            delta_occ_solds=delta_occ_solds_path,
            reference_rate=ref_price_path,
            available_capacity=avail_cap_path,
            delta_lrv=delta_lrv_path,
            window_size=self._window_size,
            calibrated_value=self._calib_values,
            use_leading_window=self._use_leading_window,
            fail_on_missing_occ_dates=self._fail_on_missing_occ_dates,
            pressure_floor=self._pressure_floor,
            minHeuristicOccPercChangeThreshold=self._minHeuristicOccPercChangeThreshold,
            persist_eval_op_at_rc_lvl=self._persist_eval_op_at_rc_lvl
        )

        assert result == expected_result
        assert cap_date == expected_capture_date
