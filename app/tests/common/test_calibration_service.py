import functools
from unittest.mock import MagicMock

import numpy as np
import pandas as pd
from pathlib import Path
import pickle

from src.common.calibration_service import CalibrationService
from src.common.dto.calibrated_potential import CalibratedPotential
from src.common.dynamodb_service import DynamoDBService
from src.common.ucs_service import UCSConfigParam
from src.algorithm.calibration_service import run_calibration, revise_threshold


class TestCalibrationService:
    _service = CalibrationService()
    _current_percentile = int(84.82142857142856)
    _expected_calibrated_percentile = np.float64(42)
    _user_waste_weight = 0.4
    _waste_threshold = 3
    _regret_threshold = 7
    _waste_regret_min_data = 5

    _new_percentile = np.float64(89.17647058823529)
    _use_leading_window = False
    _fail_on_missing_occ_dates = True
    _rolling_window = 21
    _pressure_floor = 0.0
    _min_data_req = 1

    _expected_result = np.float64(88.47619047619048)

    def _compare_partial_objects(self, p1, p2):
        return (p1.func == p2.func and
                p1.args == p2.args and
                p1.keywords == p2.keywords)

    def _compare_dicts(self, d1, d2):
        if d1.keys() != d2.keys():
            return False
        for key in d1:
            if isinstance(d1[key], functools.partial) and isinstance(d2[key], functools.partial):
                if not self._compare_partial_objects(d1[key], d2[key]):
                    return False
            else:
                if d1[key] != d2[key]:
                    return False
        return True

    def test_get_calibrated_percentile(self):
        test_idp_window_path = Path(
            __file__).parent.parent / 'algorithm' / 'DynamicOptimizationPackage' / 'data' / 'TEST_idp_window.csv'
        test_idp_window = pd.read_csv(test_idp_window_path)

        test_decision_change_path = Path(
            __file__).parent.parent / 'algorithm' / 'DynamicOptimizationPackage' / 'data' / 'TEST_decision_change.csv'
        test_decision_change = pd.read_csv(test_decision_change_path)

        fetch_idp_window_mock = MagicMock()
        DynamoDBService.fetch_idp_window = fetch_idp_window_mock
        fetch_idp_window_mock.return_value = test_idp_window

        fetch_median_abs_lrv_change_mock = MagicMock()
        DynamoDBService.fetch_median_abs_lrv_change = fetch_median_abs_lrv_change_mock
        fetch_median_abs_lrv_change_mock.return_value = test_decision_change

        get_user_waste_weight_mock = MagicMock()
        UCSConfigParam.USER_WASTE_WEIGHT.fetch_value = get_user_waste_weight_mock
        get_user_waste_weight_mock.return_value = self._user_waste_weight

        get_waste_threshold_mock = MagicMock()
        UCSConfigParam.WASTE_THRESHOLD.fetch_value = get_waste_threshold_mock
        get_waste_threshold_mock.return_value = self._waste_threshold

        get_regret_threshold_mock = MagicMock()
        UCSConfigParam.REGRET_THRESHOLD.fetch_value = get_regret_threshold_mock
        get_regret_threshold_mock.return_value = self._regret_threshold

        get_waste_regret_min_data_mock = MagicMock()
        UCSConfigParam.WASTE_REGRET_MIN_DATA.fetch_value = get_waste_regret_min_data_mock
        get_waste_regret_min_data_mock.return_value = self._waste_regret_min_data

        calibrated_percentile = self._service.get_calibrated_percentile('Hilton', 'BOSCO', self._current_percentile)

        assert self._expected_calibrated_percentile == calibrated_percentile

    def test_run_calibration(self):
        test_delta_lrv_pace_path = Path(
            __file__).parent.parent / 'algorithm' / 'DynamicOptimizationPackage' / 'data' / 'TEST_deltaLrvPace.csv'

        test_delta_occ_path = Path(
            __file__).parent.parent / 'algorithm' / 'DynamicOptimizationPackage' / 'data' / 'TEST_deltaOcc.csv'

        test_ref_price_pace_path = Path(
            __file__).parent.parent / 'algorithm' / 'DynamicOptimizationPackage' / 'data' / 'TEST_referencePricePace.csv'

        expected_result = [CalibratedPotential(accom_class_id=2, calibrated_potential=-3.3241270991582965)]

        test_quantile_func_path = Path(
            __file__).parent.parent / 'algorithm' / 'DynamicOptimizationPackage' / 'data' / 'test_quantile_function.pkl'

        with open(test_quantile_func_path, 'rb') as f1:
            expected_quantile_function = pickle.load(f1)

        result, debug_frame, quantile_function = run_calibration(
            file_reader=pd.read_csv,
            delta_occ_solds=test_delta_occ_path,
            reference_rate=test_ref_price_pace_path,
            delta_lrv=test_delta_lrv_pace_path,
            window_size=self._rolling_window,
            min_data_req=self._min_data_req,
            percentile=self._new_percentile,
            use_leading_window=self._use_leading_window,
            fail_on_missing_occ_dates=self._fail_on_missing_occ_dates,
            pressure_floor=self._pressure_floor
        )

        # assert debug_frame == expected_debug_frame is not being checked because occupancyDate col in debug_frame keeps changing for every run
        assert result == expected_result
        self._compare_dicts(quantile_function, expected_quantile_function)


    def test_revise_threshold(self):

        test_lrv_change_path = Path(
            __file__).parent.parent / 'algorithm' / 'DynamicOptimizationPackage' / 'data' / 'TEST_lrv_change.csv'

        test_lrv_change = pd.read_csv(test_lrv_change_path)

        test_quantile_func_path = Path(
            __file__).parent.parent.parent / 'tests' / 'algorithm' / 'DynamicOptimizationPackage' / 'data' / 'test_quant_func.pkl'

        with open(test_quantile_func_path, 'rb') as f1:
            thresh_fun = pickle.load(f1)

        expected_result = self._expected_result

        expected_potentials = [CalibratedPotential(accom_class_id=3, calibrated_potential=-3.024227493462277), CalibratedPotential(accom_class_id=4, calibrated_potential=-3.1356719834724887), CalibratedPotential(accom_class_id=5, calibrated_potential=-2.7481658446505826), CalibratedPotential(accom_class_id=6, calibrated_potential=-0.9676111809826138)]

        result, potentials = revise_threshold(
            waste_regret_df=test_lrv_change,
            waste_threshold=self._waste_threshold,
            regret_threshold=self._regret_threshold,
            threshold_func_by_rc=thresh_fun,
            current_calib_percentile=self._current_percentile,
            user_waste_weight=self._user_waste_weight,
            min_waste_regret_sum=self._waste_regret_min_data
        )

        assert result == expected_result
        assert potentials == expected_potentials


