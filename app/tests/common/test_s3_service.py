from unittest.mock import patch, MagicMock

import pandas as pd

from src.common.s3_service import S3Service, s3_service


class TestS3Service:

    @patch('awswrangler.s3.read_csv')
    def test_fetch_file(self, s3_mock: MagicMock) -> None:
        s3_path = 's3://mock'
        mock_frame = pd.DataFrame({'A' : [1, 2, 3]})
        s3_mock.return_value = mock_frame
        actual_frame = s3_service.fetch_file('%s' % s3_path)

        pd.testing.assert_frame_equal(actual_frame, mock_frame)

    def test_get_parent_not_ending_wth_slash(self):
        d = "s3://bucket/folder1/folder2"
        actual_parent = S3Service().get_parent(d)
        expected_parent = 's3://bucket/folder1'
        assert actual_parent == expected_parent

    def test_get_parent_ending_wth_slash(self):
        d = "s3://bucket/folder1/folder2/"
        actual_parent = S3Service().get_parent(d)
        expected_parent = 's3://bucket/folder1'
        assert actual_parent == expected_parent

    def test_get_file_name(self):
        d = "s3://bucket/folder1/folder2/"
        actual_parent = S3Service().get_file_name(d)
        expected_parent = 'folder2'
        assert actual_parent == expected_parent

    def test_get_parent_when_no_parent(self):
        d = "s3://bucket"
        actual_parent = S3Service().get_parent(d)
        expected_parent = 's3://'
        assert actual_parent == expected_parent
