import pandas as pd
from pathlib import Path
from src.algorithm.Utilities.GenericUtilities.GenericObjFileLoaders import GenericDeltaLoader
import pytest

def test_load_deltalrv_wholevalues():
    inputParms = dict(inputfilename = 'deltalrv_whole.csv',
    expectedfilename = 'deltalrv_whole.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltalrvvariable = 'deltaLrv',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate')

    pd.testing.assert_frame_equal(*process_delta_lrv_data(**inputParms))

def test_load_deltalrv_decimalvalues():
    inputParms = dict(inputfilename = 'deltalrv_decimal.csv',
    expectedfilename = 'deltalrv_decimal.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltalrvvariable = 'deltaLrv',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate')

    pd.testing.assert_frame_equal(*process_delta_lrv_data(**inputParms))

def test_load_deltalrv_zerovalues():
    inputParms = dict(inputfilename='deltalrv_zero.csv',
    expectedfilename='deltalrv_zero.csv',
    rcvariable='accomClassId',
    ltvariable='leadTime',
    deltalrvvariable='deltaLrv',
    staydatevariable='occupancyDate',
    capturedatevariable='captureDate')

    pd.testing.assert_frame_equal(*process_delta_lrv_data(**inputParms))

def test_load_deltalrv_multirc_keep_all():
    inputParms = dict(inputfilename = 'deltalrv_multirc.csv',
    expectedfilename = 'deltalrv_multirc.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltalrvvariable = 'deltaLrv',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate')

    pd.testing.assert_frame_equal(*process_delta_lrv_data(**inputParms))

def test_load_deltalrv_multirc_keep2():
    inputParms = dict(inputfilename = 'deltalrv_multirc.csv',
    expectedfilename = 'deltalrv_rc2.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltalrvvariable = 'deltaLrv',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate',
    distinctrcvariable=2)

    pd.testing.assert_frame_equal(*process_delta_lrv_data(**inputParms))

def test_load_deltalrv_norc():
    inputParms = dict(inputfilename = 'deltalrv_norc.csv',
    expectedfilename = 'deltalrv_rc2.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltalrvvariable = 'deltaLrv',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate',
    defaultrcvariable=2)

    pd.testing.assert_frame_equal(*process_delta_lrv_data(**inputParms))

def test_load_deltalrv_capturedt_dttm():
    inputParms = dict(inputfilename = 'deltalrv_capturedt_dttm.csv',
    expectedfilename = 'deltalrv_capturedt_dttm.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltalrvvariable = 'deltaLrv',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate')

    pd.testing.assert_frame_equal(*process_delta_lrv_data(**inputParms))

def test_load_deltalrv_invalidrc():
    inputParams = dict(inputfilename='deltalrv_multirc.csv',
    expectedfilename='deltalrv_multirc.csv',
    rcvariable='accomClassId',
    ltvariable='leadTime',
    deltalrvvariable='deltaLrv',
    staydatevariable='occupancyDate',
    capturedatevariable='captureDate',
    distinctrcvariable = 5)

    with pytest.raises(RuntimeError,
        match=r"No Computable intersection between distinctRC and available RC is present"):
        process_delta_lrv_data(**inputParams)

def test_load_deltalrv_missinglt():
    inputParms = dict(inputfilename = 'deltalrv_missinglt.csv',
    expectedfilename = 'deltalrv_missinglt.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltalrvvariable = 'deltaLrv',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate')

    pd.testing.assert_frame_equal(*process_delta_lrv_data(**inputParms))

def test_load_deltalrv_missingsd():
    inputParms = dict(inputfilename = 'deltalrv_missingsd.csv',
    expectedfilename = 'deltalrv_missingsd.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltalrvvariable = 'deltaLrv',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate')

    pd.testing.assert_frame_equal(*process_delta_lrv_data(**inputParms))

def test_load_refrate_wholevalues():
    inputParms = dict(inputfilename = 'refrate_whole.csv',
    expectedfilename = 'refrate_whole.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    refratevariable = 'price',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate')

    pd.testing.assert_frame_equal(*process_refrate_data(**inputParms))

def test_load_refrate_decimalvalues():
    inputParms = dict(inputfilename='refrate_decimal.csv',
    expectedfilename='refrate_decimal.csv',
    rcvariable='accomClassId',
    ltvariable='leadTime',
    refratevariable='price',
    staydatevariable='occupancyDate',
    capturedatevariable='captureDate')

    pd.testing.assert_frame_equal(*process_refrate_data(**inputParms))

def test_load_refrate_zerovalues():
    inputParms = dict(inputfilename = 'refrate_zero.csv',
    expectedfilename = 'refrate_zero.csv',
    rcvariable='accomClassId',
    ltvariable='leadTime',
    refratevariable='price',
    staydatevariable='occupancyDate',
    capturedatevariable='captureDate')


    pd.testing.assert_frame_equal(*process_refrate_data(**inputParms))

def test_load_refrate_multirc_keep_all():
    inputParms = dict(inputfilename = 'refrate_multirc.csv',
    expectedfilename = 'refrate_multirc.csv',
    rcvariable='accomClassId',
    ltvariable='leadTime',
    refratevariable='price',
    staydatevariable='occupancyDate',
    capturedatevariable='captureDate')


    pd.testing.assert_frame_equal(*process_refrate_data(**inputParms))

def test_load_refrate_multirc_multirc_keep2():
    inputParms = dict(inputfilename = 'refrate_multirc.csv',
    expectedfilename = 'refrate_rc2.csv',
    rcvariable='accomClassId',
    ltvariable='leadTime',
    refratevariable='price',
    staydatevariable='occupancyDate',
    capturedatevariable='captureDate',
    distinctrcvariable = 2)

    pd.testing.assert_frame_equal(*process_refrate_data(**inputParms))

def test_load_refrate_norc():
    inputParms = dict(inputfilename = 'refrate_norc.csv',
    expectedfilename = 'refrate_whole.csv',
    rcvariable='accomClassId',
    ltvariable='leadTime',
    refratevariable='price',
    staydatevariable='occupancyDate',
    capturedatevariable='captureDate',
    defaultrcvariable=1)

    pd.testing.assert_frame_equal(*process_refrate_data(**inputParms))

def test_load_refrate_capturedt_dttm():
    inputParms = dict(inputfilename = 'refrate_capturedt_dttm.csv',
    expectedfilename = 'refrate_rc2.csv',
    rcvariable='accomClassId',
    ltvariable='leadTime',
    refratevariable='price',
    staydatevariable='occupancyDate',
    capturedatevariable='captureDate')


    pd.testing.assert_frame_equal(*process_refrate_data(**inputParms))

def test_load_refrate_invalidrc():
    inputParams = dict(inputfilename='refrate_multirc.csv',
    expectedfilename='refrate_multirc.csv',
    rcvariable='accomClassId',
    ltvariable='leadTime',
    refratevariable='price',
    staydatevariable='occupancyDate',
    capturedatevariable='captureDate',
    distinctrcvariable = 5)

    with pytest.raises(RuntimeError,
        match=r"No Computable intersection between distinctRC and available RC is present"):
        process_refrate_data(**inputParams)

def test_load_refrate_missinglt():
    inputParms = dict(inputfilename = 'refrate_missinglt.csv',
    expectedfilename = 'refrate_missinglt.csv',
    rcvariable='accomClassId',
    ltvariable='leadTime',
    refratevariable='price',
    staydatevariable='occupancyDate',
    capturedatevariable='captureDate')

    pd.testing.assert_frame_equal(*process_refrate_data(**inputParms))

def test_load_refrate_missingsd():
    inputParms = dict(inputfilename = 'refrate_missingsd.csv',
    expectedfilename = 'refrate_missingsd.csv',
    rcvariable='accomClassId',
    ltvariable='leadTime',
    refratevariable='price',
    staydatevariable='occupancyDate',
    capturedatevariable='captureDate')

    pd.testing.assert_frame_equal(*process_refrate_data(**inputParms))

def test_load_deltaocc_wholevalues():
    inputParms = dict(inputfilename = 'deltaocc_whole.csv',
    expectedfilename = 'deltaocc_whole.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltaoccvariable = 'deltaOcc',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate')

    pd.testing.assert_frame_equal(*process_delta_occ_data(**inputParms))

def test_load_deltaocc_zerovalues():
    inputParams = dict(inputfilename = 'deltaocc_zero.csv',
    expectedfilename = 'deltaocc_zero.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltaoccvariable = 'deltaOcc',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate')

    pd.testing.assert_frame_equal(*process_delta_occ_data(**inputParams))

def test_load_deltaocc_multirc_keep_all():
    inputParms = dict(inputfilename = 'deltaocc_multirc.csv',
    expectedfilename = 'deltaocc_multirc.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltaoccvariable = 'deltaOcc',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate')

    pd.testing.assert_frame_equal(*process_delta_occ_data(**inputParms))

def test_load_deltaocc_multirc_keep2():
    inputparms=dict(inputfilename = 'deltaocc_multirc.csv',
    expectedfilename = 'deltaocc_rc2.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltaoccvariable = 'deltaOcc',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate',
    distinctrcvariable = 2)

    pd.testing.assert_frame_equal(*process_delta_occ_data(**inputparms))

def test_load_deltaocc_norc():
    inputParams = dict(inputfilename = 'deltaocc_norc.csv',
    expectedfilename =  'deltaocc_whole.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltaoccvariable = 'deltaOcc',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate',
    defaultrcvariable = 1)

    pd.testing.assert_frame_equal(*process_delta_occ_data(**inputParams))

def test_load_deltaocc_capturedt_dttm():
    inputParams = dict(inputfilename = 'deltaocc_capturedt_dttm.csv',
    expectedfilename = 'deltaocc_whole.csv',
    rcvariable='accomClassId',
    ltvariable='leadTime',
    deltaoccvariable='deltaOcc',
    staydatevariable='occupancyDate',
    capturedatevariable='captureDate')

    pd.testing.assert_frame_equal(*process_delta_occ_data(**inputParams))

def test_load_deltaocc_invalidrc():
    inputParams = dict(inputfilename='deltaocc_multirc.csv',
    expectedfilename='deltaocc_multirc.csv',
    rcvariable='accomClassId',
    ltvariable='leadTime',
    deltaoccvariable='deltaOcc',
    staydatevariable='occupancyDate',
    capturedatevariable='captureDate',
    distinctrcvariable = 5)

    with pytest.raises(RuntimeError,
        match=r"No Computable intersection between distinctRC and available RC is present"):
        process_delta_occ_data(**inputParams)

def test_load_deltaocc_missinglt():
    inputParms = dict(inputfilename = 'deltaocc_missinglt.csv',
    expectedfilename = 'deltaocc_missinglt.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltaoccvariable = 'deltaOcc',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate')

    pd.testing.assert_frame_equal(*process_delta_occ_data(**inputParms))

def test_load_deltaocc_missingsd():
    inputParms = dict(inputfilename = 'deltaocc_missingsd.csv',
    expectedfilename = 'deltaocc_missingsd.csv',
    rcvariable = 'accomClassId',
    ltvariable = 'leadTime',
    deltaoccvariable = 'deltaOcc',
    staydatevariable = 'occupancyDate',
    capturedatevariable = 'captureDate')

    pd.testing.assert_frame_equal(*process_delta_occ_data(**inputParms))

def process_delta_occ_data(inputfilename, rcvariable, ltvariable, deltaoccvariable,
                           staydatevariable, capturedatevariable, expectedfilename,
                           defaultrcvariable=1,distinctrcvariable=None):

    # Load actual delta occ data
    loaded_deltaocc = GenericDeltaLoader.loadDeltaOccFromDeltaOccFile(
        filepath=Path(__file__).parent / 'data' / inputfilename,
        filereader=GenericDeltaLoader.__determineFileTypeReader__(inputfilename),
        rcacCol=rcvariable,
        ltCol=ltvariable,
        deltaOccCol=deltaoccvariable,
        dateCol=staydatevariable,
        capDateCol=capturedatevariable,
        defaultRC=defaultrcvariable,
        distinctRC=distinctrcvariable
    )

    # Convert list of dictionaries to dataframe
    actualdfList = []
    for rc, dateDict in loaded_deltaocc.items():
        for capdate, deltaOccObj in dateDict.items():
            actualdfList.append(deltaOccObj.dataset)
            actual_deltaocc_df = pd.concat(actualdfList, ignore_index=True)
            actual_deltaocc_df = actual_deltaocc_df[[rcvariable, staydatevariable, ltvariable, deltaoccvariable, capturedatevariable]].sort_values(
        [rcvariable, staydatevariable, ltvariable], ignore_index=True)

    # Load expected data and convert to dataframe
    expected_result_fp = Path(__file__).parent / 'data' / expectedfilename
    expected_deltaocc_df = pd.read_csv(expected_result_fp)[[rcvariable, staydatevariable, ltvariable, deltaoccvariable, capturedatevariable]]
    expected_deltaocc_df = expected_deltaocc_df.sort_values([rcvariable, staydatevariable, ltvariable], ignore_index=True)

    # Convert only the date columns to datetime
    expected_deltaocc_df[staydatevariable] = pd.to_datetime(expected_deltaocc_df[staydatevariable])
    expected_deltaocc_df[capturedatevariable] = pd.to_datetime(expected_deltaocc_df[capturedatevariable])
    actual_deltaocc_df[staydatevariable] = pd.to_datetime(actual_deltaocc_df[staydatevariable])
    actual_deltaocc_df[capturedatevariable] = pd.to_datetime(actual_deltaocc_df[capturedatevariable])

    return actual_deltaocc_df, expected_deltaocc_df

def process_refrate_data(inputfilename, rcvariable, ltvariable, refratevariable,
                           staydatevariable, capturedatevariable, expectedfilename,
                           defaultrcvariable=1,distinctrcvariable=None):

    # Load actual refrate data
    loaded_refrate= GenericDeltaLoader.loadRefRateFile(
        filepath=Path(__file__).parent / 'data' / inputfilename,
        filereader=GenericDeltaLoader.__determineFileTypeReader__(inputfilename),
        rcacCol=rcvariable,
        ltCol=ltvariable,
        refRateCol=refratevariable,
        dateCol=staydatevariable,
        capDateCol=capturedatevariable,
        defaultRC=defaultrcvariable,
        distinctRC=distinctrcvariable
    )

    # Convert list of dictionaries to dataframe
    actualdfList = []
    for rc, dateDict in loaded_refrate.items():
        for capdate, refrateObj in dateDict.items():
            actualdfList.append(refrateObj.dataset)
            actual_refrate_df = pd.concat(actualdfList, ignore_index=True)
            actual_refrate_df = actual_refrate_df[[rcvariable, staydatevariable, ltvariable, refratevariable]].sort_values(
        [rcvariable, staydatevariable, ltvariable], ignore_index=True)

    # Load expected data and convert to dataframe
    expected_result_fp = Path(__file__).parent / 'data' / expectedfilename
    expected_refrate_df = pd.read_csv(expected_result_fp)[[rcvariable, staydatevariable, ltvariable, refratevariable]]
    expected_refrate_df = expected_refrate_df.sort_values([rcvariable, staydatevariable, ltvariable], ignore_index=True)

    # Convert only the date columns to datetime
    expected_refrate_df[staydatevariable] = pd.to_datetime(expected_refrate_df[staydatevariable])
    actual_refrate_df[staydatevariable] = pd.to_datetime(actual_refrate_df[staydatevariable])

    return actual_refrate_df, expected_refrate_df

def process_delta_lrv_data(inputfilename, rcvariable, ltvariable, deltalrvvariable,
                           staydatevariable, capturedatevariable, expectedfilename,
                           defaultrcvariable=1, distinctrcvariable=None):
    # Load actual delta lrv data
    loaded_deltalrv = GenericDeltaLoader.loadDeltaLRVFromDeltaLRVFile(
        filepath=Path(__file__).parent / 'data' / inputfilename,
        filereader=GenericDeltaLoader.__determineFileTypeReader__(inputfilename),
        rcacCol=rcvariable,
        ltCol=ltvariable,
        deltaLRVCol=deltalrvvariable,
        dateCol=staydatevariable,
        capDateCol=capturedatevariable,
        defaultRC=defaultrcvariable,
        distinctRC=distinctrcvariable
    )

    # Convert list of dictionaries to dataframe
    actualdfList = []
    for rc, dateDict in loaded_deltalrv.items():
        for capdate, deltalrvObj in dateDict.items():
            actualdfList.append(deltalrvObj.dataset)
            actual_deltalrv_df = pd.concat(actualdfList, ignore_index=True)
            actual_deltalrv_df = actual_deltalrv_df[
                [rcvariable, staydatevariable, ltvariable, deltalrvvariable]].sort_values(
                [rcvariable, staydatevariable, ltvariable], ignore_index=True)

    # Load expected data and convert to dataframe
    expected_result_fp = Path(__file__).parent / 'data' / expectedfilename
    expected_deltalrv_df = pd.read_csv(expected_result_fp)[[rcvariable, staydatevariable, ltvariable, deltalrvvariable]]
    expected_deltalrv_df = expected_deltalrv_df.sort_values([rcvariable, staydatevariable, ltvariable],ignore_index=True)

    # Convert only the date columns to datetime
    expected_deltalrv_df[staydatevariable] = pd.to_datetime(expected_deltalrv_df[staydatevariable])
    actual_deltalrv_df[staydatevariable] = pd.to_datetime(actual_deltalrv_df[staydatevariable])

    return actual_deltalrv_df, expected_deltalrv_df



