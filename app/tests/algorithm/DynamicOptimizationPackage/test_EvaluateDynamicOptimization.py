from pathlib import Path
import pathlib
from src.algorithm.dynamic_optimization_package.EvaluateDynamicOptimization import EvaluateDynamicOptimization
from src.algorithm.Utilities.GenericUtilities.GenericObjFileLoaders import GenericDeltaLoader
from src.algorithm.Utilities.ElevateUtilities.ElevateDefaultParams import ElevateDefaultParams

def test_singlerc_evaltrue():
    result, assemblagelist = runEval(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_default_scd.csv',
        deltalrvfilename='deltalrv_default_scd.csv',
        refratefilename='refrate_default_scd.csv',
        calibratedvalue = {1.: -3},
        windowSize=4
    )
    # Assert results
    evalFlag = result[1.0]
    assert evalFlag[0] == True, f"The evalFlag value should be True for result"
    assert evalFlag[1].strftime("%Y-%m-%d") == '2024-07-31', f"Expected evalFlag[1] to be '2024-07-31', but got {evalFlag[1]}"
def test_singlerc_evalfalse():
    result, assemblagelist = runEval(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_default_scd.csv',
        deltalrvfilename='deltalrv_default_scd.csv',
        refratefilename='refrate_default_scd.csv',
        calibratedvalue = {1.: 3},
        windowSize=4
    )

    # Assert results
    evalFlag = result[1.0]
    assert evalFlag[0] == False, f"The evalFlag value should be True for result"
def test_singlerc_calib999():
    result, assemblagelist = runEval(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_default_scd.csv',
        deltalrvfilename='deltalrv_default_scd.csv',
        refratefilename='refrate_default_scd.csv',
        calibratedvalue = {1.: 999999999},
        windowSize=4
    )
    # Assert results
    evalFlag = result[1.0]
    assert evalFlag[0] == False, f"The evalFlag value should be True for result"
def test_multirc_evaltrue():
    result, assemblagelist = runEval(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirc_scd.csv',
        deltalrvfilename='deltalrv_multirc_scd.csv',
        refratefilename='refrate_multirc_scd.csv',
        calibratedvalue = {1.: -2,2.: -2},
        windowSize=4
    )
    # Assert results
    evalFlag = result[1.0]
    assert evalFlag[0] == True, f"The evalFlag value should be True for result"
    assert evalFlag[1].strftime(
        "%Y-%m-%d") == '2024-08-09', f"Expected evalFlag[1] to be '2024-07-31', but got {evalFlag[1]}"

    evalFlag = result[2.0]
    assert evalFlag[0] == True, f"The evalFlag value should be True for result"
    assert evalFlag[1].strftime(
        "%Y-%m-%d") == '2024-08-10', f"Expected evalFlag[1] to be '2024-07-31', but got {evalFlag[1]}"
def test_multirc_outside_occthres():
    result, assemblagelist = runEval(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirc_scd.csv',
        deltalrvfilename='deltalrv_multirc_scd.csv',
        refratefilename='refrate_multirc_scd.csv',
        calibratedvalue = {1.: -2,2.: -2},
        minHeuristicOccPercChangeThreshold=.6,
        windowSize=4
    )

    # Assert results
    evalFlag = result[1.0]
    assert evalFlag[0] == False, f"The evalFlag value should be True for result"

    evalFlag = result[2.0]
    assert evalFlag[0] == False, f"The evalFlag value should be True for result"
def test_multirc_inside_occthres():
    result, assemblagelist = runEval(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirc_scd.csv',
        deltalrvfilename='deltalrv_multirc_scd.csv',
        refratefilename='refrate_multirc_scd.csv',
        calibratedvalue = {1.: -2,2.: -2},
        minHeuristicOccPercChangeThreshold=.4,
        windowSize=4
    )

    # Assert results
    evalFlag = result[1.0]
    assert evalFlag[0] == True, f"The evalFlag value should be True for result"
    assert evalFlag[1].strftime(
        "%Y-%m-%d") == '2024-08-09', f"Expected evalFlag[1] to be '2024-07-31', but got {evalFlag[1]}"

    evalFlag = result[2.0]
    assert evalFlag[0] == True, f"The evalFlag value should be True for result"
    assert evalFlag[1].strftime(
        "%Y-%m-%d") == '2024-08-10', f"Expected evalFlag[1] to be '2024-07-31', but got {evalFlag[1]}"
def test_no_availCapacity_occthres_set():
    result, assemblagelist = runEval(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_default_scd.csv',
        deltalrvfilename='deltalrv_default_scd.csv',
        refratefilename='refrate_default_scd.csv',
        calibratedvalue = {1.: -3},
        minHeuristicOccPercChangeThreshold=.4,
        windowSize=4
    )
    # Assert results
    evalFlag = result[1.0]
    assert evalFlag[0] == True, f"The evalFlag value should be True for result"
    assert evalFlag[1].strftime("%Y-%m-%d") == '2024-07-31', f"Expected evalFlag[1] to be '2024-07-31', but got {evalFlag[1]}"
def test_neg_availCapacity():
    result, assemblagelist = runEval(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirc_ac0_scd.csv',
        deltalrvfilename='deltalrv_default_scd.csv',
        refratefilename='refrate_default_scd.csv',
        calibratedvalue = {1.: -3},
        minHeuristicOccPercChangeThreshold=.4,
        windowSize=4
    )
    # Assert results
    evalFlag = result[1.0]
    assert evalFlag[0] == True, f"The evalFlag value should be True for result"
    assert evalFlag[1].strftime("%Y-%m-%d") == '2024-07-31', f"Expected evalFlag[1] to be '2024-07-31', but got {evalFlag[1]}"
def test_pressure_floor1():
    result, assemblagelist = runEval(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirc_scd.csv',
        deltalrvfilename='deltalrv_multirc_scd.csv',
        refratefilename='refrate_multirc_scd.csv',
        calibratedvalue = {1.: -2,2.: -2},
        windowSize=4,
        pressure_floor=.25
    )
    # Assert results
    evalFlag = result[1.0]
    assert evalFlag[0] == True, f"The evalFlag value should be True for result"
    assert evalFlag[1].strftime(
        "%Y-%m-%d") == '2024-08-07', f"Expected evalFlag[1] to be '2024-07-31', but got {evalFlag[1]}"

    evalFlag = result[2.0]
    assert evalFlag[0] == True, f"The evalFlag value should be True for result"
    assert evalFlag[1].strftime(
        "%Y-%m-%d") == '2024-08-10', f"Expected evalFlag[1] to be '2024-07-31', but got {evalFlag[1]}"
def test_pressure_floor2():
    result, assemblagelist = runEval(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirc_scd.csv',
        deltalrvfilename='deltalrv_multirc_scd.csv',
        refratefilename='refrate_multirc_scd.csv',
        calibratedvalue = {1.: -2,2.: -2},
        windowSize=4,
        pressure_floor=1
    )
    # Assert results
    evalFlag = result[1.0]
    assert evalFlag[0] == False, f"The evalFlag value should be False for result"

    evalFlag = result[2.0]
    assert evalFlag[0] == False, f"The evalFlag value should be False for result"

def runEval(basedir:pathlib.Path,deltaoccfilename: str, windowSize: int, deltalrvfilename: str,refratefilename: str, calibratedvalue: str,
            pressure_floor: float = 0.0, minHeuristicOccPercChangeThreshold: float = 0.0):
    calibrated_value = calibratedvalue
    occman = GenericDeltaLoader.loadDeltaOccFromDeltaOccFile(filepath=basedir/deltaoccfilename,
                                                    filereader=GenericDeltaLoader.__determineFileTypeReader__(deltaoccfilename)
                                                    ,defaultRC=ElevateDefaultParams.defaultRoomClass
                                                    ,rcacCol='accomClassId'
                                                    ,ltCol='leadTime'
                                                    ,deltaOccCol='deltaOcc'
                                                    ,dateCol='occupancyDate'
                                                    ,capDateCol='captureDate'
                                                    ,remainingCapacityCol = 'availableCapacity'
                                                    )
    refman = GenericDeltaLoader.loadRefRateFile(filepath=basedir/refratefilename,
                                                    filereader=GenericDeltaLoader.__determineFileTypeReader__(refratefilename)
                                                    ,defaultRC=ElevateDefaultParams.defaultRoomClass
                                                    ,rcacCol='accomClassId'
                                                    ,ltCol='leadTime'
                                                    ,refRateCol='price'
                                                    ,dateCol='occupancyDate'
                                                    ,capDateCol='captureDate')
    lrvMan = GenericDeltaLoader.loadDeltaLRVFromDeltaLRVFile(filepath=basedir/deltalrvfilename,
                                                    filereader=GenericDeltaLoader.__determineFileTypeReader__(deltalrvfilename)
                                                    ,defaultRC=ElevateDefaultParams.defaultRoomClass
                                                    ,rcacCol='accomClassId'
                                                    ,ltCol='leadTime'
                                                    ,deltaLRVCol='deltaLrv'
                                                    ,dateCol='occupancyDate'
                                                    ,capDateCol='captureDate')
    rceval = EvaluateDynamicOptimization(deltaOccHandlerDict=occman
                                         , refRateHandlerDict=refman
                                         , deltaLRVHandlerDict=lrvMan
                                         , calibrated=calibrated_value
                                         , potentialMode='slidingwindow'
                                         , windowSize=windowSize
                                         , minHeuristicOccPercChangeThreshold=minHeuristicOccPercChangeThreshold
                                         , pressure_floor =pressure_floor)
    # Perform the evaluation and store the result
    rceval.__toggleDebug__()
    result, assemblagelist = rceval.evaluate()
    rceval.__toggleDebug__()

    return result, assemblagelist
