from unittest.mock import MagicMock, call, patch

import pandas.testing as pdt
import pytest

from src.algorithm.Utilities.GenericUtilities.FillMissingDates import FillMissingDates
from src.algorithm.dynamic_optimization_package.daily_median_lrv_change_calculator import calculate_lrv_change
from tests.read_csv import ReadCsvUtil

class TestDailyMedianLrvChangeCalculator:

    change_lrv = ReadCsvUtil.read_csv('''caughtUpDate,optimizationTime,medianLrvChange
    2025-01-05,2025-01-05T09:10:11.88888,15.2
    2025-01-05,2025-01-05T11:10:11.88888,-5
    2025-01-05,2025-01-05T14:10:11.88888,9
    2025-01-05,2025-01-05T16:10:11.88888,-5
    2025-01-06,2025-01-05T13:10:11.88888,-10
    ''')

    idp_window = ReadCsvUtil.read_csv('''caughtUpDate,evaluationTime,maxOccupancyDate
    2025-01-05,2025-01-05T09:05:11.88888,2025-05-13
    2025-01-05,2025-01-05T11:03:11.88888,2025-06-13
    2025-01-05,2025-01-05T14:06:11.88888,2025-01-13
    2025-01-05,2025-01-05T16:10:11.88888,2025-03-13
    2025-01-05,2025-01-05T18:10:11.88888,2025-03-13
    2025-01-06,2025-01-05T13:10:11.88888,2025-03-13
    ''')

    expected = ReadCsvUtil.read_csv('''caughtUpDate,medianLrvChange,noOfOpt
    2025-01-05,-5.0,4
    2025-01-06,-10.0,1
    ''', date_cols=['caughtUpDate'])

    @patch("src.algorithm.Utilities.GenericUtilities.FillMissingDates.FillMissingDates.fill_missing_dates")
    def test_calculate_lrv_change(self, fill_missing_dates_mock):
        fill_missing_dates_mock.return_value = self.expected
        actual = calculate_lrv_change(self.change_lrv, self.idp_window)
        actual_args, actual_kwargs = fill_missing_dates_mock.call_args
        pdt.assert_frame_equal(actual_kwargs['df'], self.expected)
