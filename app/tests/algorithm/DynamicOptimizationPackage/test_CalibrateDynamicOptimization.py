from pathlib import Path
import pathlib
import pandas as pd
from src.algorithm.dynamic_optimization_package.CalibrateDynamicOptimization import CalibrationDynamicOptimization
from src.algorithm.Utilities.GenericUtilities.GenericObjFileLoaders import GenericDeltaLoader

def test_windowsize_singlerc():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_default.csv',
        deltalrvfilename='deltalrv_default.csv',
        refratefilename='refrate_default.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_windowsize_singlerc', result)
def test_windowsize_multirc():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirc.csv',
        deltalrvfilename='deltalrv_multirc.csv',
        refratefilename='refrate_multirc.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_windowsize_multirc', result)
def test_random_input():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_random.csv',
        deltalrvfilename='deltalrv_random.csv',
        refratefilename='refrate_random.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_random_input', result)
def test_deltalrv_decimal():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirc.csv',
        deltalrvfilename='deltalrv_multirc_decimal.csv',
        refratefilename='refrate_multirc.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_deltalrv_decimal', result)
def test_refrate_decimal():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirc.csv',
        deltalrvfilename='deltalrv_multirc.csv',
        refratefilename='refrate_multirc_decimal.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_refrate_decimal', result)
def test_multirc_refrate_ac2_zero():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirc.csv',
        deltalrvfilename='deltalrv_multirc.csv',
        refratefilename='refrate_multirczero.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_multirc_refrate_ac2_zero', result)
def test_multirc_deltaocc_ac2_zero():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirczero.csv',
        deltalrvfilename='deltalrv_multirc.csv',
        refratefilename='refrate_multirc.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_multirc_deltaocc_ac2_zero', result)
def test_multirc_deltalrv_ac2_zero():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirc.csv',
        deltalrvfilename='deltalrv_multirczero.csv',
        refratefilename='refrate_multirc.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_multirc_deltalrv_ac2_zero', result)
def test_single_captureDate():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_default_scd.csv',
        deltalrvfilename='deltalrv_default_scd.csv',
        refratefilename='refrate_default_scd.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_single_captureDate', result)
def test_calibQuantile1():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_default.csv',
        deltalrvfilename='deltalrv_default.csv',
        refratefilename='refrate_default.csv',
        windowSize=4,
        calibQuantile = 1
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_calibQuantile1', result)
def test_calibQuantile100():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_default.csv',
        deltalrvfilename='deltalrv_default.csv',
        refratefilename='refrate_default.csv',
        windowSize=4,
        calibQuantile = 100
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_calibQuantile100', result)
def test_deltaocc_zero():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_zero.csv',
        deltalrvfilename='deltalrv_default.csv',
        refratefilename='refrate_default.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_zero', result)
def test_deltalrv_zero():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_default.csv',
        deltalrvfilename='deltalrv_zero.csv',
        refratefilename='refrate_default.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_zero', result)
def test_refrate_zero():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_default.csv',
        deltalrvfilename='deltalrv_default.csv',
        refratefilename='refrate_zero.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_zero', result)
def test_ceiling_zero(): #ceiling zero on all days
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_default.csv',
        deltalrvfilename='deltalrv_ceiling_zero.csv',
        refratefilename='refrate_default.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_zero', result)
def test_ceiling_zero1(): #ceiling zero on some days
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_default.csv',
        deltalrvfilename='deltalrv_ceiling_zero1.csv',
        refratefilename='refrate_default.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_ceiling_zero1', result)
def test_ceiling_zero2(): #ceiling zero up until the end
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_default.csv',
        deltalrvfilename='deltalrv_ceiling_zero2.csv',
        refratefilename='refrate_default.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_ceiling_zero2', result)
def test_multirc_refrate_all_zero():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirc.csv',
        deltalrvfilename='deltalrv_multirc.csv',
        refratefilename='refrate_multirczero1.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_zero_2ac', result)
def test_multirc_deltaLRV_all_zero():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirc.csv',
        deltalrvfilename='deltalrv_multirczero1.csv',
        refratefilename='refrate_multirc.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_zero_2ac', result)
def test_multirc_deltaOcc_all_zero():
    result, assemblagelist = runCalib(
        basedir=Path(__file__).parent / 'data',
        deltaoccfilename='deltaocc_multirczero1.csv',
        deltalrvfilename='deltalrv_multirc.csv',
        refratefilename='refrate_multirc.csv',
        windowSize=4
    )
    validate_data(assemblagelist, 'test_CalibrateDynamicOptimization.xlsx', 'test_zero_2ac', result)
def validate_data(assemblagelist, assemblagefilename, assemblagesheetname, result):

    # Construct the file path relative to the script
    data_file_path = Path(__file__).parent / 'data' / assemblagefilename

    # Load the expected calibration results
    expected_result_df = pd.read_excel(
        data_file_path,
        sheet_name=assemblagesheetname
    )[['acId', 'calibresult']]
    expected_result_df = expected_result_df[expected_result_df['acId'].notna() & (expected_result_df['acId'] != "")]

    # Sort expected results by 'accomClassId'
    expected_result_df = expected_result_df.sort_values(['acId'], ignore_index=True)

    # Get unique values from the 'acId' column
    unique_rc_values = expected_result_df['acId'].unique()

    # Iterate over unique values
    for actual_calibvalue in unique_rc_values:

        # Filter DataFrame for the current 'acId' value
        filtered_df = expected_result_df.query(f"acId == {actual_calibvalue}")

        # Return expected value
        expected_calibvalue = filtered_df['calibresult'].tolist()[0]

        # Assert results for actual and expected calibrated value
        assert round(result[actual_calibvalue], 5) == expected_calibvalue, \
            f"FAIL: Calibration value comparison failed for {assemblagesheetname}  accomClassId={actual_calibvalue}"
        # If assertion passes
        print(f"PASS: Calibration value comparison passed for {assemblagesheetname} accomClassId={actual_calibvalue}.")
def runCalib(basedir: pathlib.Path, windowSize: int, deltaoccfilename: str, deltalrvfilename: str,
             refratefilename: str, calibQuantile: float = 50):
    occman = GenericDeltaLoader.loadDeltaOccFromDeltaOccFile(filepath=basedir / deltaoccfilename,
                                                filereader=GenericDeltaLoader.__determineFileTypeReader__(
                                                deltaoccfilename)
                                                , defaultRC=-1
                                                , rcacCol='accomClassId'
                                                , ltCol='leadTime'
                                                , deltaOccCol='deltaOcc'
                                                , dateCol='occupancyDate'
                                                , capDateCol='captureDate'
                                                )
    refman = GenericDeltaLoader.loadRefRateFile(filepath=basedir / refratefilename,
                                                filereader=GenericDeltaLoader.__determineFileTypeReader__(
                                                refratefilename)
                                                , defaultRC=-1
                                                , rcacCol='accomClassId'
                                                , ltCol='leadTime'
                                                , refRateCol='price'
                                                , dateCol='occupancyDate'
                                                , capDateCol='captureDate')
    lrvMan = GenericDeltaLoader.loadDeltaLRVFromDeltaLRVFile(filepath=basedir / deltalrvfilename,
                                                filereader=GenericDeltaLoader.__determineFileTypeReader__(
                                                deltalrvfilename)
                                                , defaultRC=-1
                                                , rcacCol='accomClassId'
                                                , ltCol='leadTime'
                                                , deltaLRVCol='deltaLrv'
                                                , dateCol='occupancyDate'
                                                , capDateCol='captureDate')
    cdo = CalibrationDynamicOptimization(deltaOccHandlerDict=occman
                                         , refRateHandlerDict=refman
                                         , deltaLRVHandlerDict=lrvMan
                                         , potentialMode='slidingwindow'
                                         , windowSize=windowSize
                                         , calibQuantile=calibQuantile
                                         )
    cdo.__toggleDebug__()
    calibrated, assemblagelist, extra_info = cdo.calibrate()

    return calibrated, assemblagelist
