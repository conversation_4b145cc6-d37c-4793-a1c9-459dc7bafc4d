# Introduction.
This is the backend code for the Dynamic Optimization. 

# Calculating Potential
This takes place in 3 steps. On a particular capturedate:
1) get the `deltaOB`, `deltaLRV`, and `refrate` for the entire foreward looking window.
   2) when evaluating the `deltaOB` it is the change in OnBooks since the last optimization
   3) When calibrating the `deltaOB` is the change in onbooks across an entire leadtime and the `deltaLRV` is the active BDE during that day (so it will have been generated the day before I believe)
2) for each day compute the quantity `(a_i)^2 = (deltaOB*deltaLRV/refRate)^2`
4) compute `potential = ln(sum (a_i)^2)` for each possible window of up to length `nWindowsize`. For example if you have 21 values of `a_i` with a windowsize of 20, you will compute 2 potentials, if you have a windowsize of 7, you will compute 7 separate potentials. By doing it this way you allow yourself to cover any potential window you may need. If your windowsize is 21 and you only have 14 days in which `(a_i)^2>0` then you only have one potential of size 14
    - Discard any window where `sum (a_i)^2=0`
# General Idea
## Calibration
Here we take all possible windows and take the 50th percentile of observed potentials

## Evaluation
This will only take a look at the forward looking window. 
1) subset any window in which the potential>calibratedThreshold
2) select the earliest window for which the potential exceeds the threshold
3) Optimize up to the endDate of that window+maxLos


# Efficiency
## Profile
On my SAS Developer system:
- Eval Avg Run Time: 0.016 seconds
  - approx 72% of processing time was spent on I/O, mostly on the I/O portion
  - per roomclass per hotel
  - This will easily scale to 1million evaluations, taking approx 1.75 hours total processing time per day on a single cpu. 
- Calibration takes on average about 7 seconds per property and rc. Again most of the time is spent in I/O, but I did not evaluate how much. 

## Potential Optimizations
- Switching to Polars instead of pandas may yield substantial benefits for I/O, however this may not be entirely necessary. 