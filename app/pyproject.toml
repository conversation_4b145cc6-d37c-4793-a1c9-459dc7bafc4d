[tool.poetry]
name = "dynamic-optimization-service"
version = "0.1.0"
description = "Python service to evaluate when to trigger IDP using calibrated potetials and handle requests for calibration of potential."
authors = ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>",
            "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>",
            "<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.12"
pandas = "^2.2.2"
fastapi = {extras = ["standard"], version = "^0.115.0"}
awswrangler = "^3.9.1"
gunicorn = "^23.0.0"
ddtrace = "^2.16.3"
aws-lambda-powertools = "^3.3.0"
requests = "^2.32.3"
openpyxl = "^3.1.5"
pytz = "^2024.2"
fsspec = "^2025.5.1"
s3fs = "^2025.5.1"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.2"
pytest-cov = "^5.0.0"
httpx = "^0.27.2"


[tool.poetry.group.test.dependencies]
pylint = "^3.3.1"

[tool.pytest.ini_options]
# `.` & 'tests' to include any util functions used only tests
# '../../.' to add root directory in pypath because usually we open root directory in intellij.
#    So all imports start from root dir..
pythonpath= ['.', 'tests', '../../.', 'src', 'resources']


[tool.poetry.scripts]
lint = "pylint.__init__:run_pylint"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
