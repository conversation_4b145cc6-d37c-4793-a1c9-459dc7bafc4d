repos:
#######
# ALL #
#######
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v4.5.0
  hooks:
    # Git style
    - id: check-added-large-files
    - id: check-merge-conflict
    - id: check-vcs-permalinks
    - id: forbid-new-submodules
    - id: no-commit-to-branch
    - id: end-of-file-fixer
    - id: mixed-line-ending
      args: [--fix=lf]

    # Common errors
    - id: trailing-whitespace
      args: [--markdown-linebreak-ext=md]

    # Cross platform
    - id: check-case-conflict

    # Security
    - id: detect-private-key

#############
# TERRAFORM #
#############
- repo: git://github.com/antonbabenko/pre-commit-terraform
  rev: v1.88.0
  hooks:
    - id: terraform_fmt
    - id: terraform_docs
      args:
          - --args=--anchor=false
          - --args=--html=false
