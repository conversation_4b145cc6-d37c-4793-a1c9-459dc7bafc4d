import json
import requests
import uuid
from datetime import datetime
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed

API_URL = "https://dyn-opt-analytics-internal.dev.ideasrms.com/report/event-sns"
HEADERS = {"Content-Type": "application/json"}

# Property codes for evaluation events
EVALUATION_PROPS = ["BOSCO", "ADBMA", "LAX02", "DALTX"]
EVAL_TOTAL = 1
CALIB_TOTAL = 1


def create_eval_event(client_code: str, property_code: str) -> dict:
    """
    Declaratively define the evaluation event payload.
    """
    return {
        "sourceSystem": "dyn-opt",
        "scope": "env",
        "version": "1",
        "eventDateTime": datetime.utcnow().isoformat() + "Z",
        "eventSource": {
            "clientCode": client_code,
            "propertyCode": property_code,
            "requestContext": {
                "deltaOccSolds": "dyn-opt-property-incremental-solds",
                "referenceRate": "dyn-opt-ref-price-latest",
                "deltaLrv": "dyn-opt-delta-lrv-latest",
                "minDataRequirement": 1,
                "maxDataLimit": 28,
                "calibratedPotential": "dyn-opt-calibrated-potentials"
            },
        },
        "eventType": "DYN_OPT_EVALUATE_FOR_IDP"
    }


def create_calibration_event(client_code: str, property_code: str) -> dict:
    """
    Declaratively define the calibration-style SNS event payload.
    """
    return {
        "sourceSystem": "dyn-opt",
        "scope": "env",
        "version": "1",
        "eventDateTime": datetime.utcnow().isoformat() + "Z",
        "eventSource": {
            "clientCode": client_code,
            "propertyCode": property_code,
            "requestContext": {
                "deltaOccSolds": f"s3://dev-dyn-opt-data/{client_code}/{property_code}/calibration/2025-09-18/deltaOcc.csv",
                "referenceRate": f"s3://dev-dyn-opt-data/{client_code}/{property_code}/calibration/2025-09-18/referencePricePace.csv",
                "deltaLrv": f"s3://dev-dyn-opt-data/{client_code}/{property_code}/calibration/2025-09-18/deltaLrvPace.csv",
                "minDataRequirement": 1,
                "maxDataLimit": 28
            },
            "destinationTable": "dyn-opt-calibrated-potentials"
        },
        "eventType": "DYN_OPT_POTENTIAL_CALIBRATION"
    }


def post_event(event: dict):
    """
    Send a POST request to the target API.
    """
    try:
        response = requests.post(API_URL, headers=HEADERS, data=json.dumps(event), timeout=10)
        return response.status_code
    except Exception as e:
        return f"Error: {e}"


def main():
    client_code = "Hilton"

    print(f"🚀 Launching {EVAL_TOTAL} evaluation + {CALIB_TOTAL} calibration events concurrently...")

    with ThreadPoolExecutor(max_workers=20) as executor:
        futures = []

        # Schedule evaluation events
        for i in range(EVAL_TOTAL):
            prop = EVALUATION_PROPS[i % len(EVALUATION_PROPS)]
            futures.append(executor.submit(post_event, create_eval_event(client_code, prop)))

        # Schedule calibration events
        for i in range(CALIB_TOTAL):
            prop = EVALUATION_PROPS[i % len(EVALUATION_PROPS)]
            futures.append(executor.submit(post_event, create_calibration_event(client_code, prop)))

        # Collect results
        for i, future in enumerate(as_completed(futures), start=1):
            result = future.result()
            print(f"[{i:03}] → {result}")

    print("✅ All events dispatched.")


if __name__ == "__main__":
    main()
