terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    datadog = {
      source  = "DataDog/datadog"
      version = "~> 3.0"
    }
  }
}

provider "aws" {
  region = "us-east-2"
  default_tags {
    tags = {
      Service    = var.service_name
      Env        = var.environment
      Terraform  = "true"
      Repository = var.repository_name
    }
  }
}

provider "aws" {
  alias  = "dnsdelegate"
  region = "us-east-2"
  assume_role {
    role_arn     = "arn:aws:iam::${local.shared_service_account_id}:role/avm-dnsdelegate"
    session_name = "AVMDNS"
  }
}

# DataDog app key to register the service in the service catalog
# DataDog app key to register the service in the service catalog
provider "datadog" {
  api_key = module.shared_config.datadog_api_key
  app_key = module.shared_config.datadog_app_key
}
