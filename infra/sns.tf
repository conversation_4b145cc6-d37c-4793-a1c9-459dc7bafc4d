resource "aws_sns_topic_subscription" "uen_dyn_opt_calibration_req_subscription" {
  topic_arn     = var.fds_uen_topic_arn
  protocol      = "sqs"
  endpoint      = module.dyn_opt_calibration_req.queue_arn
  filter_policy = <<EOF
{
  "sourceSystem": ["dyn-opt", "regression-automation"],
  "eventType": [
    "DYN_OPT_POTENTIAL_CALIBRATION",
    "GENERATE_ASSEMBLAGE_MR",
    "DYN_OPT_REVISE_POTENTIAL"
  ]
}
EOF
}

resource "aws_sns_topic_subscription" "uen_dyn_opt_evaluation_req_subscription" {
  topic_arn     = var.fds_uen_topic_arn
  protocol      = "sqs"
  endpoint      = module.dyn_opt_evaluation_req.queue_arn
  filter_policy = <<EOF
{
  "sourceSystem": ["dyn-opt", "regression-automation"],
  "eventType": [
    "DYN_OPT_EVALUATE_FOR_IDP"
  ]
}
EOF
}


resource "aws_sqs_queue_policy" "allow_uen_to_send_events_calib_queue" {
  queue_url = module.dyn_opt_calibration_req.queue_url
  policy    = <<EOF
{
  "Version": "2012-10-17",
  "Id": "sqspolicy",
  "Statement": [
    {
      "Sid": "Allow unified notification sns send to ups client create sqs",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "sqs:SendMessage",
      "Resource": "${module.dyn_opt_calibration_req.queue_arn}",
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "${var.fds_uen_topic_arn}"
        }
      }
    }
  ]
}
EOF
}

resource "aws_sqs_queue_policy" "allow_uen_to_send_events_eval_queue" {
  queue_url = module.dyn_opt_evaluation_req.queue_url
  policy    = <<EOF
{
  "Version": "2012-10-17",
  "Id": "sqspolicy",
  "Statement": [
    {
      "Sid": "Allow unified notification sns send to ups client create sqs",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "sqs:SendMessage",
      "Resource": "${module.dyn_opt_evaluation_req.queue_arn}",
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "${var.fds_uen_topic_arn}"
        }
      }
    }
  ]
}
EOF
}
