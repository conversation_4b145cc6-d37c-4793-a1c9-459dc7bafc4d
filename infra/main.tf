################################################################################
# Reference architecture - Springboot Microservice

# ------------------------------------------------------------------------------
# My Service, "Fast, good, cheap: pick any two.”
# ------------------------------------------------------------------------------
module "dyn_opt_analytics_service" {
  # ADD YOUR DESIRED MODULE VERSION TO THIS URL AFTER FORKING
  # In the form of ?ref=v1.0.0 (version tag of your choice)
  source = "git::https://github.com/ideasorg/terraform-aws-containerapp-base.git?ref=v5"

  # Parent module must have an AWS provider authenticated as role/avm-dnsdelegate in the
  # shared services account for management of the application's DNS record.
  providers = {
    aws.dnsdelegate = aws.dnsdelegate
  }

  service_name         = var.service_name
  service_version      = var.service_version
  environment          = module.shared_config.environment
  internal_fqdn_prefix = "${var.service_name}-internal"

  # Scale settings for your containers; these are configurable
  # on a per-environment basis in app_settings/.  You should
  # change the settings there, not here.
  container_min_count  = var.container_min_count
  container_max_count  = var.container_max_count
  per_container_cpu    = var.per_container_cpu
  per_container_memory = var.per_container_memory
  capacity_provider    = var.capacity_provider
  spot_weight          = var.spot_weight
  spot_minimum         = var.spot_minimum
  nonspot_weight       = var.nonspot_weight
  nonspot_minimum      = var.nonspot_minimum

  # Autoscaling settings
  autoscaling = var.autoscaling

  # Extra container environment variables
  container_environment_variables = [
    {
      name  = "S3_DATA_BUCKET_NAME",
      value = var.dyn_opt_s3_bucket
    },
    {
      name  = "ENV",
      value = module.shared_config.environment
    },
    {
      name  = "LRV_TABLE_NAME",
      value = var.dynamodb_table[1]
    },
    {
      name  = "AVAILABLE_CAPACITY_TABLE",
      value = var.dynamodb_table[7]
      }, {
      name  = "IDP_COUNT_TABLE",
      value = var.dynamodb_table[4]
      }, {
      name  = "IDP_WINDOW_TABLE",
      value = var.dynamodb_table[5]
    },
    {
      name  = "INC_SOLDS_TABLE_NAME",
      value = var.dynamodb_table[2]
    },
    {
      name  = "CALIBRATED_POTENTIAL_TABLE_NAME",
      value = var.dynamodb_table[3]
    },
    {
      name  = "REFERENCE_PRICE_TABLE_NAME",
      value = var.dynamodb_table[0]
    },
    {
      name  = "LOG_LEVEL",
      value = var.log_level
      }, {
      name  = "CALIB_REQ_URL",
      value = module.dyn_opt_calibration_req.queue_url
      }, {
      name  = "EVALUATION_REQ_URL",
      value = module.dyn_opt_evaluation_req.queue_url
      }, {
      name  = "FDS_UEN_ARN",
      value = var.fds_uen_topic_arn
      }, {
      name  = "UCS_BASE_URL",
      value = var.ucs_base_url
      }, {
      name  = "UPS_BASE_URL",
      value = var.ups_base_url
      }, {
      name  = "UIS_AUTH_URL",
      value = var.uis_auth_url
      }, {
      name  = "AWS_REGION",
      value = var.aws_region
      }, {
      name  = "ECS_CLUSTER_NAME",
      value = var.ecs_cluster_name
      }, {
      name  = "ECS_SERVICE_NAME",
      value = var.ecs_service_name
      }, {
      name  = "SECRET_MANAGER_NAME",
      value = var.secret_manager_name
      }, {
      name  = "DECISION_CHANGE_TABLE",
      value = var.dynamodb_table[6]
      }, {
      name  = "CALIB_THREAD_COUNT",
      value = var.calib_thread_count
      }, {
      name  = "EVALUATION_THREAD_COUNT",
      value = var.eval_thread_count
    }
  ]

  # Roles to attach to this service
  ecs_policies = [
    aws_iam_policy.sqs_access.arn,
    "arn:aws:iam::aws:policy/AmazonSNSFullAccess",
    "arn:aws:iam::aws:policy/AmazonS3FullAccess",
    "arn:aws:iam::aws:policy/AmazonDynamoDBFullAccess",
    "arn:aws:iam::${module.shared_config.account_id}:policy/dyn-opt-secret-value-access-policy",
    "arn:aws:iam::${module.shared_config.account_id}:policy/secret-value-access-policy"
  ]

  container_ulimits = {
    nofile  = 65536
    memlock = -1
  }

  dd_index = var.dd_index

  # Create DataDog service definition
  team_name = var.team_name
  dd_service_contacts = [
    {
      name    = "MS Teams Channel Email"
      contact = var.teams_channel_email
    }
  ]
  repo_url = var.repo_url
  container_healthcheck_commandlist = [
    "CMD-SHELL",
    "curl -f http://localhost:8000/health-check || exit 1"
  ]
  container_ports = {
    internal = {
      port                      = 8000
      alb_create_listener       = "internal"
      alb_listen_port           = 443
      alb_health_check_path     = "/health-check"
      alb_health_check_interval = 10
      alb_healthy_threshold     = 2
    }
  }
}
