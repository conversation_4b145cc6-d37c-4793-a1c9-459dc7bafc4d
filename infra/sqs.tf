module "dyn_opt_calibration_req" {
  source                      = "terraform-aws-modules/sqs/aws"
  version                     = "~> 4.2.0"
  name                        = "dyn-opt-calibration-req"
  visibility_timeout_seconds  = 300
  fifo_queue                  = false
  content_based_deduplication = false
  create_dlq                  = true
  redrive_policy = {
    maxReceiveCount = 3
  }
  sqs_managed_sse_enabled = true
}

module "dyn_opt_evaluation_req" {
  source                      = "terraform-aws-modules/sqs/aws"
  version                     = "~> 4.2.0"
  name                        = "dyn-opt-evaluation-req"
  visibility_timeout_seconds  = 300
  fifo_queue                  = false
  content_based_deduplication = false
  create_dlq                  = true
  redrive_policy = {
    maxReceiveCount = 3
  }
  sqs_managed_sse_enabled = true
}
