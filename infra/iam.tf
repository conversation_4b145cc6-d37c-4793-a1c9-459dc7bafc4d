resource "aws_iam_policy" "sqs_access" {
  name = "sqsAccessToEcsService"
  policy = jsonencode(
    {
      "Version" : "2012-10-17",
      "Statement" : [
        {
          "Effect" : "Allow",
          "Action" : [
            "sqs:ReceiveMessage",
            "sqs:SendMessage",
            "sqs:DeleteMessage"
          ],
          "Resource" : [module.dyn_opt_calibration_req.queue_arn, module.dyn_opt_evaluation_req.queue_arn]
        },
        {
          "Effect" : "Allow",
          "Action" : [
            "ecs:ListTasks",
            "ecs:DescribeTasks"
          ],
          "Resource" : "*"
        }
      ]
    }
  )
}
