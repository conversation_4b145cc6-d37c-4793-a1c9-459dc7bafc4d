- [\[IDeaS\] Springboot Microservice](#ideas-springboot-microservice)
  - [What is implemented here?](#what-is-implemented-here)
  - [How do I use this repository?](#how-do-i-use-this-repository)
    - [1. Create a repository](#1-create-a-repository)
    - [2. Add reviewers (codeowners)](#2-add-reviewers-codeowners)
    - [3. Change environment variables](#3-change-environment-variables)
    - [4. Check terraform related variables](#4-check-terraform-related-variables)
    - [5. Remove unused infrastructure](#5-remove-unused-infrastructure)
    - [6. Using Base Image in Your Dockerfile](#6-using-base-image-in-your-dockerfile)
    - [7. Develop your service and run it in the ☁️](#7-develop-your-service-and-run-it-in-the-️)
  - [CI/CD workflow details](#cicd-workflow-details)
    - [build\_and\_deploy.yml](#build_and_deployyml)
    - [manual\_deploy.yml](#manual_deployyml)
  - [Fast feedback and visibility](#fast-feedback-and-visibility)
  - [Dependabot](#dependabot)
  - [How much does it cost?](#how-much-does-it-cost)
  - [How do I choose the right capacity provider strategy?](#how-do-i-choose-the-right-capacity-provider-strategy)
  - [What are the major components of this repository?](#what-are-the-major-components-of-this-repository)
  - [Spring Cloud Integration](#spring-cloud-integration)
  - [MS Teams Notifications](#ms-teams-notifications)
  - [Security and Linting](#security-and-linting)
    - [Terraform](#terraform)
  - [Reference Documentation](#reference-documentation)

# [IDeaS] Springboot Microservice

## What is implemented here?

This repository builds and deploys a Springboot project built using Maven with both Sonar and Artifactory integration. It deploys as a container to AWS ECS FarGate fronted using a application load balancer with HTTPS support.
It assumes the project to be:

- Spring Boot/Web version 2.7.2
- JSON Logging
- Maven version 3.8.7
- Java version 17
- Swagger documentation
- Lombok
- Spring Security Resource Server 5.7.2

The deployment leverages ECS/Fargate. Container logs and metrics are all forwarded automatically to DataDog.

## How do I use this repository?

Like any Reference Architecture repository, you can launch it into your own AVM-based project using the following steps:

### 1. Create a repository

[ideas-github-management](https://github.com/ideasorg/ideas-github-management#repositories-management)
> **Note:** You should set `ideasorg/ideas-springboot-microservice` as the template repository.

### 2. Add reviewers (codeowners)

**Where**: [CODEOWNERS](./.github/CODEOWNERS) usually your team.

**How**: [CODEOWNERS](https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners)

### 3. Change environment variables

[general.env](./.github/variables/general.env). These variables are going to be used in [workflow.yml](.github/workflows/workflow.yml) as workflow's inputs values.

- `AWS_ACCOUNT_ID_DEV` - AWS account ID of DEV environment.
- `AWS_ACCOUNT_ID_STAGE` - AWS account ID of STAGE environment.
- `AWS_ACCOUNT_ID_PROD` - AWS account ID of PROD environment.
- `TF_DIR` - Directory where Terraform files are located.
- `TF_STATE_NAME` - Terraform state file name.
- `SERVICE_NAME` - Service name.
- `TEAM_NAME` - Team name (will be used in DataDog's `Service Catalog`).
- `TEAMS_CHANNEL_EMAIL` - MS Teams channel email (will be used in DataDog's `Service Catalog`).
- `INITIAL_RUN` - Specific variable that should be set to true only the first run for each environment. (resolving `Chicken or the egg` problem with AWS ECR).
- `JAVA_VERSION` - Java version.
- `TF_FMT_AND_DOCS` - Whether to run [terraform fmt](https://developer.hashicorp.com/terraform/cli/commands/fmt) and [terraform-docs](https://github.com/terraform-docs/terraform-docs).
- `TF_VALIDATE` - Whether to run [terraform validate](https://developer.hashicorp.com/terraform/cli/commands/validate).
- `TFLint` - Whether to run [tflint](https://github.com/terraform-linters/tflint).
- `TFSec` - Whether to run [tfsec](https://github.com/aquasecurity/tfsec).

### 4. Check terraform related variables

There is a folder for terraform variables: [infra/env](infra/env). Check this folder for terraform vars, and set up them as you need for each environment.

### 5. Remove unused infrastructure

`EFS` - if you don't plan to use EFS in your project, you should remove it from [main.tf](https://github.com/ideasorg/ideas-springboot-microservice/blob/56b96947fee641e7cd9b935c334a1df5e51e50a9/infra/main.tf#L5-L45) and [main.tf](https://github.com/ideasorg/ideas-springboot-microservice/blob/56b96947fee641e7cd9b935c334a1df5e51e50a9/infra/main.tf#L131-L144)

`SecretsManager` - included just to demonstrate how you can use secrets for your service. [main.tf](https://github.com/ideasorg/ideas-springboot-microservice/blob/56b96947fee641e7cd9b935c334a1df5e51e50a9/infra/main.tf#L47-L70) and [main.tf](https://github.com/ideasorg/ideas-springboot-microservice/blob/56b96947fee641e7cd9b935c334a1df5e51e50a9/infra/main.tf#L120-L125)

`IAM policy` - combined with SecretsManager example to demonstrate how you can add permissions to your service. [iam.tf](https://github.com/ideasorg/ideas-springboot-microservice/blob/56b96947fee641e7cd9b935c334a1df5e51e50a9/infra/iam.tf#L1-L18) and [main.tf](https://github.com/ideasorg/ideas-springboot-microservice/blob/56b96947fee641e7cd9b935c334a1df5e51e50a9/infra/main.tf#L128)

### 6. Using Base Image in Your Dockerfile

In your Dockerfile, there is a predefined base image that is pulled from a repository named [ideas-java-images](https://github.com/ideasorg/ideas-java-images) You have two options for how to use this base image, depending on your requirements:

- #### Use the Predefined Base Image as Is (Major Version)

    By default, you can use the predefined base image as is. This means that you will use the major version of the base image, and any updates and improvements made to this image's major version will be automatically applied to your Docker image.
    In this case, you will always have the latest major version of the base image with its updates without manual intervention. This option is suitable for applications that can accommodate automatic updates.
    TODO: add documentation about dependabot

- #### Use the Predefined Base Image with a Specific Version

    Alternatively, you can choose to use a specific version of the predefined base image. This allows you to have more control over your image's dependencies, and updates to the base image won't be automatically applied.
    In this case, you specify a specific version of the base image to ensure that your application remains consistent with the version you've chosen.
    TODO: add documentation about dependabot

### 7. Develop your service and run it in the ☁️

Change code in [src](./src) directory. And update other java related stuff.

## CI/CD workflow details

We have two workflow files in the `.github` folder: `build_and_deploy.yml` and `manual_deploy.yml`

### build_and_deploy.yml

This is the main configuration file for our workflow runs. It contains multiple jobs with multiple steps. Let's look into them.

- #### params (Main and PR)

  - sets up env. variables from files. Files are located in the `.github/variables/` folder.

- #### verify (PR)

  - performs lints and checks (mainly terraform related now)
  > if your terraform code is not formatted or README is not updated, this check will fix it automatically during the workflow run by commiting the fixed code, after you just need to do `git pull` and get fixed code.

- #### BUILD (PR)

  - Do the build.
  > For more details look at [README.md](https://github.com/ideasorg/ideas-github-workflows/blob/main/.github/workflows/README_SPRINGBOOT_MAVEN_BUILD.md)

- #### RELEASE_MAIN_IMAGE (Main)

  - Retag the image to the merge commit.
  > For more details look at [README.md](https://github.com/ideasorg/ideas-github-workflows/blob/main/.github/workflows/README_SPRINGBOOT_RELEASE_MAIN.md)

- #### DEV_DEPLOY (PR)

  - Deploy to DEV environment.
  > For more details look at [README.md](https://github.com/ideasorg/ideas-github-workflows/blob/main/.github/workflows/README_SPRINGBOOT_DEPLOY.md)

- #### STAGE_DEPLOY (Main)

  - Deploy to STAGE environment.
  > For more details look at [README.md](https://github.com/ideasorg/ideas-github-workflows/blob/main/.github/workflows/README_SPRINGBOOT_DEPLOY.md)

- #### PROD_DEPLOY (Main)

  - Deploy to PROD environment.
  > For more details look at [README.md](https://github.com/ideasorg/ideas-github-workflows/blob/main/.github/workflows/README_SPRINGBOOT_DEPLOY.md)

### manual_deploy.yml

This workflow is intended for manual deployment. Intended to run on rare occasions when you need to deploy a specific commit to a specific environment. For example, you need to deploy a hotfix to PROD. You can run this workflow manually and specify the commit and environment.

If you want to run this workflow, you have to go to repo's `Actions` section, choose `[Manual] Deploy` Workflow, choose on which environment you want to deploy, specify commit SHA to deploy. See the picture below.

In case you want to deploy an old image that were not used for more than 3 week, you have to check `Resurrect image from ECR before deploy` checkbox. This will pull the image from ECR, Push it to GHCR and deploy it to the environment.
> This will work only if you have pushed the image previously to ECR.

<img alt="alt text" height="400" src="ideas-springboot-deploy.png" width="350"/>

## Fast feedback and visibility

GitHub offers a good set of tools to get fast feedback and visibility. We use them to get fast feedback on our code and infrastructure changes. We also use them to get visibility on our infrastructure and services.

**No boring logs to read** - we develop our actions and reusable workflows the way that you can get the most important information right in your PR comments or in the Job Summary without reading logs.

For example:

- **Terraform**
  - _plan_ - you can see the changes in the PR comment, you will see additional labels added to your PR based on the changes. You can also see the changes as a graph in the Job Summary.
  - _apply_ - you can see results of apply in the PR comment, you will also see terraform outputs after apply in the Job Summary.
- **Checks and Lints**
  - _Checks & Lints_ summary in the Job Summary
  - _tflint_ and _tfsec_ warnings and errors will be added as comments to your PR.
- **Unit tests** - you can see the results of unit tests in the PR comment and in the Job Summary.
- **SonarCloud** - you can see the results of SonarCloud analysis in the PR comment.
- **General**
  - all environment variables in the Job Summary (for debugging purposes)
  - all workflow's inputs in the Job Summary if jobs rerun with `debug` flag (for debugging purposes)
  - some useful service related info for specific commit (like environment, version, PR name) in the Job Summary

## Dependabot

We use dependabot for dependencies updates.
Dependabot will check for dependency's updates and open PR when it's necessary. For more details check [dependabot.yml](./.github/dependabot.yml).

## How much does it cost?

Certain aspects of this architecture, such as container size, are customizable based on environment (dev/stage/prod) and have a large impact on cost. You may wish to configure them in `infra/env/`. The following table shows the default cost as-configured, but your mileage may vary depending on scale.

| Component | Cost On-Demand | Cost Spot | Cost Prepaid | Reference                                            |
|:----------|:---------------|:----------|:-------------|:-----------------------------------------------------|
| Fargate   | $36.06         | $11.49    | $17.31       | <https://aws.amazon.com/fargate/pricing/>              |
| ALB       | $16.44         | $16.44    | $16.44       | <https://aws.amazon.com/elasticloadbalancing/pricing/> |
| Minus EDP | -$6.83         | -$3.56    | -$4.39       |                                                      |
| Total     | $45.68         | $23.80    | $29.36       |                                                      |

Costs are per-account, per-month; multiply costs by three for dev, stage, and production.  Prepaid costs assume
a three year contract paid upfront; other configurations available.

For the Fargate instances, we have currently defaulted our stage and dev environments to use FARGATE_SPOT as capacity providers. We continue to use FARGATE as our capacity_provider for our production environment. This is primarily due to price advantages that FARGATE_SPOT has to offer. FARGATE_SPOT is cheaper but comes at the risk of instances being terminated at a 2-minute notice. Feel free to switch out of FARGATE_SPOT for dev and stage if that suits you best. This can be done by making changes to the files in the following path: infra/env.

## How do I choose the right capacity provider strategy?

- The base value designates how many tasks, at a minimum, to run on the specified capacity provider. Only one capacity provider in a capacity provider strategy can have a base defined. This corresponds to spot_minimum and nonspot_minimum variables in our repo.

- The weight value designates the relative percentage of the total number of launched tasks that should use the specified capacity provider. This corresponds to spot_weight and nonspot_weight variables in our repo.

For example, if you have a strategy that contains two capacity providers, and both have a weight of 1, then when the base is satisfied, the tasks will be split evenly across the two capacity providers. Using that same logic, if you specify a weight of 1 for capacityProviderA and a weight of 4 for capacityProviderB, then for every one task that is run using capacityProviderA, four tasks would use capacityProviderB.

From <https://docs.aws.amazon.com/AmazonECS/latest/developerguide/cluster-capacity-providers.html>

_Data charges may apply. Logs sold separately, secrets not included._

## What are the major components of this repository?

The repository is broken up into two main pieces, the `infra/` and `src/` directories.  Inside `infra/` you will find all the terraform code
required to deploy your code to an AWS AVM account. Inside `infra` the most important file for you is `main.tf`.
Inside it, you will find:

- `module "efs"` - an EFS Filesystem to demonstrate EFS functionality of _containerapp-base_ module. Feel free to remove it after your repository creation if your app doesn't need an EFS.
- `module "service"` - it is the heart of this reference architecture. This module is used to create all needed resources for your ECS service. Most of the configurations could be done through parameters of this module (discussed above).

> You can find more details on how to use this module in the module's repository [terraform-aws-containerapp-base](https://github.com/ideasorg/terraform-aws-containerapp-base/blob/main/README.md)

## Spring Cloud Integration

Spring cloud provides a very abstract way of interacting with the AWS Services without any boilerplate code

- You can connect to different services like SQS, SNS, RDS etc
- It needs some additional config and dependencies that needs to be added
- Sprint Cloud uses AWS credentials using which it interacts with the AWS Services
- These credentials can be given in two ways:
  - Access Key and Sec Key generated from GetAWSKey utility(Mainly useful in running locally)
        `cloud.aws.credentials.instanceProfile=false
        cloud.aws.credentials.use-default-aws-credentials-chain=true
        AWS_PROFILE=********-developer`
  - Use the assumed role which is assigned to the ECS(Preferred way when deployed in AWS AVM Accounts)
        `cloud.aws.credentials.instanceProfile=true`
- Preferred version of the Spring Cloud is: Greenwich.SR3
- Mvn Build config needs to be changed in POM.XML as application using the Spring Cloud wont start locally as it explicitly looks for AWS creds on startup
- Ref Link: [Spring Cloud AWS](https://spring.io/projects/spring-cloud-aws)

## MS Teams Notifications

Right now we don't have MS Teams integration, but it will be in the future.

## Security and Linting

### Terraform

- `terraform validate` - validates the configuration files in a directory, referring only to the configuration and not accessing any remote services such as remote state, provider APIs, etc.
  - Validate runs checks that verify whether a configuration is syntactically valid and internally consistent, regardless of any provided variables or existing state. It is thus primarily useful for general verification of reusable modules, including correctness of attribute names and value types.
- `terraform-docs` - generates Terraform modules documentation in various formats
- `terraform fmt` - check Terraform configuration files against a canonical format and style [Terraform language style conventions](https://www.terraform.io/docs/language/syntax/style.html)
- `tflint` - a pluggable Terraform linter. TFLint is a framework and each feature is provided by plugins, the key features are as follows:
  - Find possible errors (like illegal instance types) for Major Cloud providers (AWS/Azure/GCP).
  - Warn about deprecated syntax, unused declarations.
  - Enforce best practices, naming conventions.
  > Rules used in this project:
  > - [Common rules](https://github.com/terraform-linters/tflint/blob/master/docs/rules/README.md)
  > - [AWS Provider rules](https://github.com/terraform-linters/tflint-ruleset-aws/blob/master/docs/rules/README.md)
- `tfsec` - tfsec uses static analysis of your terraform configurations to spot potential security issues.

## Reference Documentation

For further reference, please consider the following sections:

- [Official Apache Maven documentation](https://maven.apache.org/guides/index.html)
- [Spring Boot Maven Plugin Reference Guide](https://docs.spring.io/spring-boot/docs/2.4.0-M2/maven-plugin/reference/html/)
- [Create an OCI image](https://docs.spring.io/spring-boot/docs/2.4.0-M2/maven-plugin/reference/html/#build-image)
- [Swagger documentation once app is up](http://localhost:8080/swagger-ui-custom.html)
- [GitHub documentation](https://docs.github.com/en)
  - [Events that trigger workflows](https://docs.github.com/en/actions/using-workflows/events-that-trigger-workflows) [ pull_request, push, workflow_dispatch ]
  - [Using jobs in a workflow](https://docs.github.com/en/actions/using-jobs/using-jobs-in-a-workflow)
  - [Reusing workflows](https://docs.github.com/en/actions/using-workflows/reusing-workflows)
  - [Manually running a workflow](https://docs.github.com/en/actions/managing-workflow-runs/manually-running-a-workflow)
